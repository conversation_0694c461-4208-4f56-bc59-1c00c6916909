/* Pane management and layout */
.pane {
  transition: all 0.3s ease-in-out;
  position: relative;
  flex-shrink: 0;
}

/* Draggable resizer between panes */
.resizer {
  width: 6px;
  cursor: col-resize;
  background: transparent;
  flex-shrink: 0;
  position: relative;
  transition: background-color 0.15s ease-in-out;
}
.resizer:hover,
.resizer.is-dragging {
  background: rgba(59,130,246,0.35);
}

/* Collapsed pane */
.pane.collapsed {
  width: 32px !important;
  min-width: 32px !important;
  max-width: 32px !important;
  flex-shrink: 0;
  flex-grow: 0;
  overflow: visible;
  border-right-width: 0;
  border-left-width: 0;
}
.pane.collapsed .pane-content {
  opacity: 0;
  pointer-events: none;
  overflow: hidden;
}
.pane:not(.collapsed) {
  flex-grow: 1;
}

/* Toggle buttons between panes */
.pane-toggle {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;
  background: var(--bg-secondary);
  border: 2px solid var(--border-color);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 10px rgba(0,0,0,0.15);
}
.pane-toggle:hover {
  background: var(--bg-primary);
  transform: translateY(-50%) scale(1.08);
  border-color: #3b82f6;
  box-shadow: 0 6px 16px rgba(59,130,246,0.25);
}
.pane-toggle.left { right: -16px; }
.pane-toggle.right { left: -16px; }
.pane-toggle svg {
  transition: transform 0.3s ease;
  width: 18px;
  height: 18px;
  color: #334155;
}
html.dark .pane-toggle svg { color: #e2e8f0; }
.pane-toggle::after {
  content: "";
  position: absolute;
  inset: -4px;
  border-radius: 9999px;
  box-shadow: 0 0 0 2px rgba(59,130,246,0.0);
  transition: box-shadow 0.2s ease;
}
.pane-toggle:hover::after {
  box-shadow: 0 0 0 2px rgba(59,130,246,0.35);
}

/* Specific pane growth rules */
#files-pane:not(.collapsed) {
  flex-grow: 0;
  flex-shrink: 0;
}
#code-pane:not(.collapsed),
#preview-pane:not(.collapsed) {
  flex: 1 1 0;
  min-width: 250px;
}
