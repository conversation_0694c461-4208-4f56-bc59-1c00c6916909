:root {
  --bg-primary: #f8fafc;
  --bg-secondary: #ffffff;
  --text-primary: #0f172a;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --font-sans: 'Inter', sans-serif;
  --font-mono: 'Fira Code', monospace;
}
html.dark {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --text-primary: #f1f5f9;
  --text-secondary: #94a3b8;
  --border-color: #334155;
}
body {
  font-family: var(--font-sans);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  overflow: hidden;
}
.editor-container { position: relative; }
#html-input, #syntax-highlight {
  font-family: var(--font-mono);
  font-size: 14px;
  line-height: 1.5;
  position: absolute;
  top: 0; left: 0;
  width: 100%; height: 100%;
  padding: 1rem;
  margin: 0;
  border: none;
  overflow: auto;
}
#html-input {
  resize: none;
  background: transparent;
  color: transparent;
  caret-color: var(--text-primary);
  z-index: 1;
}
#syntax-highlight {
  pointer-events: none;
  z-index: 0;
}
.toast {
  animation: slideIn 0.3s ease-out, fadeOut 0.3s ease-in 3.7s forwards;
}
@keyframes slideIn { from { transform: translateY(-100%); opacity: 0; } to { transform: translateY(0); opacity: 1; } }
@keyframes fadeOut { from { opacity: 1; } to { opacity: 0; } }
.drag-over {
  outline: 2px dashed #3b82f6;
  outline-offset: -4px;
}
/* Status indicators */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}
.status-indicator.saved { background: #10b981; }
.status-indicator.modified { background: #f59e0b; }
.status-indicator.error { background: #ef4444; }
/* Enhanced file tree base font */
#file-tree {
  font-size: 13px;
  line-height: 1.2;
}
/* Improved file tree scrollbar */
#file-tree::-webkit-scrollbar { width: 4px; }
#file-tree::-webkit-scrollbar-track { background: transparent; }
#file-tree::-webkit-scrollbar-thumb { background: var(--border-color); border-radius: 2px; }
#file-tree::-webkit-scrollbar-thumb:hover { background: var(--text-secondary); }
