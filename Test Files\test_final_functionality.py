#!/usr/bin/env python3
"""
Final comprehensive test for HTML Previewer Pro after folder explorer optimization.
Ensures all functionality remains intact and improvements are working.
"""

import os
import re

def test_html_structure():
    """Test the basic HTML structure and required elements."""
    
    html_file = "HTML_Previewer.html"
    if not os.path.exists(html_file):
        return False, "HTML file not found"
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_elements = [
        ("HTML5 Doctype", "<!DOCTYPE html>"),
        ("Files Pane", 'id="files-pane"'),
        ("Code Pane", 'id="code-pane"'),
        ("Preview Pane", 'id="preview-pane"'),
        ("Project Explorer", "Project Explorer"),
        ("File Tree", 'id="file-tree"'),
        ("HTML Input", 'id="html-input"'),
        ("Preview Frame", 'id="preview-frame"'),
        ("Syntax Highlighting", 'id="syntax-highlight"'),
        ("Dark Mode Toggle", 'id="dark-mode-toggle"'),
    ]
    
    missing = []
    for name, selector in required_elements:
        if selector not in content:
            missing.append(name)
    
    if missing:
        return False, f"Missing elements: {', '.join(missing)}"
    
    return True, "All required elements found"

def test_folder_explorer_optimization():
    """Test that folder explorer optimizations are properly applied."""
    
    html_file = "HTML_Previewer.html"
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    optimizations = {
        "Reduced pane width": 'width: 250px',
        "Compact header": 'class="p-2 border-b',
        "Optimized tree margin": 'margin-left: 12px;',
        "Compact file items": 'padding: 3px 6px;',
        "Custom scrollbar": '::-webkit-scrollbar',
        "Max height constraint": 'max-h-96',
        "Reduced font size": 'font-size: 13px;',
        "Compact buttons": 'gap-0.5',
        "Smaller toggles": 'width: 14px;',
    }
    
    results = {}
    for opt_name, selector in optimizations.items():
        results[opt_name] = selector in content
    
    passed = sum(results.values())
    total = len(results)
    
    return passed, total, results

def test_javascript_functionality():
    """Test that essential JavaScript functionality is present."""
    
    html_file = "HTML_Previewer.html"
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    js_components = [
        ("FileManager class", "class FileManager"),
        ("EditorController class", "class EditorController"),
        ("PreviewEngine class", "class PreviewEngine"),
        ("PanelManager class", "class PanelManager"),
        ("PersistenceManager class", "class PersistenceManager"),
        ("Event listeners", "addEventListener"),
        ("Keyboard shortcuts", "keydown"),
        ("File operations", "handleFolderSelection"),
        ("Panel toggle", "togglePanel"),
        ("Dark mode", "toggleDarkMode"),
    ]
    
    missing_js = []
    for name, pattern in js_components:
        if pattern not in content:
            missing_js.append(name)
    
    if missing_js:
        return False, f"Missing JS: {', '.join(missing_js)}"
    
    return True, "All JavaScript components found"

def test_css_integrity():
    """Test that CSS styles are properly maintained."""
    
    html_file = "HTML_Previewer.html"
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    css_components = [
        ("Dark mode variables", "html.dark"),
        ("Pane management", ".pane"),
        ("File tree styling", ".file-item, .folder-item"),
        ("Tree structure", ".tree-children"),
        ("Context menu", ".context-menu"),
        ("Modal styles", ".modal"),
        ("Status indicators", ".status-indicator"),
        ("Custom scrollbar", "::-webkit-scrollbar"),
        ("Folder toggle", ".folder-toggle"),
        ("Responsive design", "@media"),
    ]
    
    missing_css = []
    for name, selector in css_components:
        if selector not in content:
            missing_css.append(name)
    
    if missing_css:
        return False, f"Missing CSS: {', '.join(missing_css)}"
    
    return True, "All CSS components found"

def validate_file_size():
    """Check if file size is reasonable."""
    
    html_file = "HTML_Previewer.html"
    file_size = os.path.getsize(html_file)
    size_mb = file_size / (1024 * 1024)
    
    if size_mb > 10:  # Arbitrary reasonable limit
        return False, f"File too large: {size_mb:.2f}MB"
    
    return True, f"File size OK: {size_mb:.2f}MB"

if __name__ == "__main__":
    print("🚀 Final Comprehensive Test")
    print("=" * 50)
    
    # Test HTML structure
    print("🏗️  Testing HTML Structure...")
    html_ok, html_msg = test_html_structure()
    print(f"   {'✅' if html_ok else '❌'} {html_msg}")
    
    # Test folder explorer optimizations
    print("\n📁 Testing Folder Explorer Optimizations...")
    opt_passed, opt_total, opt_results = test_folder_explorer_optimization()
    print(f"   ✅ {opt_passed}/{opt_total} optimizations applied")
    for opt_name, applied in opt_results.items():
        status = "✅" if applied else "❌"
        print(f"      {status} {opt_name}")
    
    # Test JavaScript
    print("\n⚡ Testing JavaScript Functionality...")
    js_ok, js_msg = test_javascript_functionality()
    print(f"   {'✅' if js_ok else '❌'} {js_msg}")
    
    # Test CSS
    print("\n🎨 Testing CSS Integrity...")
    css_ok, css_msg = test_css_integrity()
    print(f"   {'✅' if css_ok else '❌'} {css_msg}")
    
    # Test file size
    print("\n📏 Testing File Size...")
    size_ok, size_msg = validate_file_size()
    print(f"   {'✅' if size_ok else '❌'} {size_msg}")
    
    # Overall results
    all_tests = [html_ok, opt_passed == opt_total, js_ok, css_ok, size_ok]
    passed_tests = sum(all_tests)
    total_tests = len(all_tests)
    
    print(f"\n📊 Final Results:")
    print(f"   Tests Passed: {passed_tests}/{total_tests}")
    print(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    print(f"   Folder Explorer Optimizations: {opt_passed}/{opt_total}")
    
    if passed_tests == total_tests and opt_passed >= opt_total * 0.9:
        print(f"\n🎉 SUCCESS: All tests passed!")
        print(f"   HTML Previewer Pro is fully optimized and functional.")
        print(f"   Folder explorer improvements successfully implemented.")
    elif passed_tests >= total_tests * 0.8:
        print(f"\n✅ GOOD: Most tests passed.")
        print(f"   Application should work correctly with minor improvements.")
    else:
        print(f"\n⚠️  WARNING: Some critical issues found.")
        print(f"   Review test results and fix identified problems.")
    
    print(f"\n🎯 Status: {'OPTIMIZED AND READY' if passed_tests == total_tests else 'NEEDS REVIEW'}")