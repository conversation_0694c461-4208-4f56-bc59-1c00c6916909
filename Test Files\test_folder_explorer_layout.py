#!/usr/bin/env python3
"""
Test script to verify folder explorer layout improvements.
This script tests the HTML_Previewer.html file for folder explorer styling and layout issues.
"""

import os
import re

def check_folder_explorer_layout():
    """Check the current folder explorer layout and identify issues."""
    
    html_file = "HTML_Previewer.html"
    if not os.path.exists(html_file):
        print(f"❌ {html_file} not found!")
        return False
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    issues_found = []
    improvements_made = []
    
    print("🔍 Analyzing Folder Explorer Layout...")
    print("=" * 50)
    
    # Check 1: Files pane width
    files_pane_match = re.search(r'id="files-pane"[^>]*style="([^"]*)"', content)
    if files_pane_match:
        style = files_pane_match.group(1)
        print(f"📐 Files pane style: {style}")
        if "width: 280px" in style:
            issues_found.append("Files pane width might be too wide (280px)")
    
    # Check 2: Project Explorer padding
    project_explorer_padding = re.search(r'<div class="p-3[^>]*>.*?<h2[^>]*>Project Explorer</h2>', content, re.DOTALL)
    if project_explorer_padding:
        print("📦 Project Explorer header has p-3 padding (12px)")
        if "p-3" in content:
            issues_found.append("Project Explorer header uses p-3 (might be excessive)")
    
    # Check 3: File tree padding
    file_tree_match = re.search(r'id="file-tree"[^>]*class="([^"]*)"', content)
    if file_tree_match:
        classes = file_tree_match.group(1)
        print(f"🌳 File tree classes: {classes}")
        if "p-2" in classes:
            issues_found.append("File tree uses p-2 padding")
    
    # Check 4: Select folder button styling
    select_btn_match = re.search(r'id="select-folder-btn"[^>]*class="([^"]*)"', content)
    if select_btn_match:
        classes = select_btn_match.group(1)
        print(f"🔘 Select folder button classes: {classes}")
        if "py-2" in classes:
            issues_found.append("Select folder button has vertical padding py-2")
    
    # Check 5: File item padding
    file_item_css = re.search(r'\.file-item, \.folder-item \{[^}]*padding: ([^;]+);', content)
    if file_item_css:
        padding = file_item_css.group(1)
        print(f"📁 File/folder item padding: {padding}")
        if padding == "4px 8px":
            print("✅ File item padding is reasonable")
        else:
            issues_found.append(f"File item padding might need adjustment: {padding}")
    
    # Check 6: Tree children margin
    tree_children_css = re.search(r'\.tree-children \{[^}]*margin-left: ([^;]+);', content)
    if tree_children_css:
        margin = tree_children_css.group(1)
        print(f"🌲 Tree children margin: {margin}")
        if margin == "16px":
            issues_found.append("Tree children margin might be too large (16px)")
    
    # Check 7: Overall file pane structure
    print("\n📋 Current File Pane Structure:")
    print("   - Header (Project Explorer): p-3 padding")
    print("   - Select button container: p-2 padding")
    print("   - File tree: p-2 padding + flex-1 overflow-y-auto")
    print("   - Footer (Save All): p-2 padding")
    
    print(f"\n❗ Issues Found ({len(issues_found)}):")
    for i, issue in enumerate(issues_found, 1):
        print(f"   {i}. {issue}")
    
    if not issues_found:
        print("✅ No obvious layout issues detected!")
        
    return len(issues_found) == 0

def test_folder_explorer_functionality():
    """Test folder explorer basic functionality by checking required elements."""
    
    html_file = "HTML_Previewer.html"
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_elements = [
        ('Project Explorer header', 'Project Explorer'),
        ('Select folder button', 'id="select-folder-btn"'),
        ('File tree container', 'id="file-tree"'),
        ('New file button', 'id="new-file-btn"'),
        ('New folder button', 'id="new-folder-btn"'),
        ('Import file button', 'id="import-file-btn"'),
        ('Save all button', 'id="save-all-btn"'),
        ('Folder input', 'id="folder-input"'),
        ('Files pane toggle', 'id="files-toggle"'),
    ]
    
    print("\n🧪 Testing Folder Explorer Elements...")
    print("=" * 50)
    
    all_present = True
    for name, selector in required_elements:
        if selector in content:
            print(f"✅ {name}: Found")
        else:
            print(f"❌ {name}: Missing")
            all_present = False
    
    return all_present

def suggest_improvements():
    """Suggest specific improvements for the folder explorer."""
    
    print("\n💡 Suggested Improvements:")
    print("=" * 50)
    
    suggestions = [
        "1. Reduce files pane width from 280px to 250px for more content space",
        "2. Change header padding from p-3 to p-2 for compactness",
        "3. Reduce tree children margin from 16px to 12px",
        "4. Make select folder button more compact (reduce py-2 to py-1.5)",
        "5. Optimize spacing between sections",
        "6. Consider making file tree container use less padding",
        "7. Add max-height to file tree to prevent excessive vertical space",
        "8. Improve button spacing and sizing"
    ]
    
    for suggestion in suggestions:
        print(f"   {suggestion}")

if __name__ == "__main__":
    print("🚀 Folder Explorer Layout Test")
    print("=" * 50)
    
    layout_ok = check_folder_explorer_layout()
    functionality_ok = test_folder_explorer_functionality()
    
    print(f"\n📊 Test Results:")
    print(f"   Layout Issues: {'None' if layout_ok else 'Found'}")
    print(f"   Functionality: {'✅ OK' if functionality_ok else '❌ Issues'}")
    
    if not layout_ok:
        suggest_improvements()
    
    print(f"\n📈 Overall Status: {'✅ PASS' if layout_ok and functionality_ok else '❌ NEEDS IMPROVEMENT'}")