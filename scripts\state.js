export const state = {
  loadedFiles: new Map(),
  virtualFiles: new Map(),
  folders: new Set(),
  folderExpansion: new Map(),
  currentFile: null,
  currentFilePath: null,
  editorContent: '',
  isDirty: false,
  settings: {
    darkMode: localStorage.getItem('htmlPreviewerDarkMode') === 'true',
    lastFolder: localStorage.getItem('htmlPreviewerLastFolder'),
    panelStates: JSON.parse(localStorage.getItem('htmlPreviewerPanelStates') || '{"files": false, "code": false, "preview": false}'),
    panelSizes: JSON.parse(localStorage.getItem('htmlPreviewerPanelSizes') || '{"filesWidth": 300, "codeWidth": null}')
  },
  modifiedFiles: new Set()
};

export function saveSettings() {
  localStorage.setItem('htmlPreviewerDarkMode', state.settings.darkMode);
  localStorage.setItem('htmlPreviewerLastFolder', state.settings.lastFolder || '');
  localStorage.setItem('htmlPreviewerPanelStates', JSON.stringify(state.settings.panelStates));
  localStorage.setItem('htmlPreviewerPanelSizes', JSON.stringify(state.settings.panelSizes || {}));
}
