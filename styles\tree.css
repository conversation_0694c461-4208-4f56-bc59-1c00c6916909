/* File tree and items */
.file-item, .folder-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: 3px 6px;
  border-radius: 3px;
  cursor: pointer;
  user-select: none;
  font-size: 13px;
  line-height: 1.3;
}
.file-item:hover, .folder-item:hover { background: var(--bg-primary); }
.file-item.active {
  background: #3b82f6;
  color: #ffffff;
}
.file-item.modified::after {
  content: "●";
  color: #f59e0b;
  margin-left: auto;
}

/* Tree structure */
.tree-item {
  display: flex;
  align-items: center;
  gap: 6px;
}
.tree-children {
  margin-left: 16px;
  border-left: none;
  padding-left: 0;
}
.tree-children.collapsed { display: none; }

/* Folder expand/collapse caret */
.folder-toggle {
  width: 14px;
  height: 14px;
  margin-right: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
  color: var(--text-secondary);
  flex-shrink: 0;
}
.folder-toggle.expanded { transform: rotate(90deg); }
.folder-toggle svg { width: 10px; height: 10px; }

/* Keep labels on one line and truncate */
.tree-item > span.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1 1 auto;
}
/* Ensure SVG icons don't shrink oddly */
.tree-item svg { flex-shrink: 0; }

/* Indentation by depth */
.tree-item[data-depth="1"] { margin-left: 0px; }
.tree-item[data-depth="2"] { margin-left: 12px; }
.tree-item[data-depth="3"] { margin-left: 24px; }
.tree-item[data-depth="4"] { margin-left: 36px; }
.tree-item[data-depth="5"] { margin-left: 48px; }
