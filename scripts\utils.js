export const ErrorCodes = {
  INVALID_NAME: 'E_INVALID_NAME',
  DUPLICATE_PATH: 'E_DUPLICATE_PATH',
  NOT_FOUND: 'E_NOT_FOUND',
  READ_FAILED: 'E_READ_FAILED',
  ZIP_NOT_LOADED: 'E_ZIP_NOT_LOADED',
  NO_MODIFIED: 'E_NO_MODIFIED',
  INVALID_HTML: 'E_INVALID_HTML',
  ABORTED: 'E_ABORTED',
  UNKNOWN: 'E_UNKNOWN'
};

export function debounce(fn, wait) {
  let t;
  return (...args) => {
    clearTimeout(t);
    t = setTimeout(() => fn.apply(this, args), wait);
  };
}

export function isBasicHtml(str) {
  return /<html[\s>]/i.test(str) && /<\/html>/i.test(str);
}

export function showToast(message, type = 'info') {
  const container = document.getElementById('toast-container');
  if (!container) return;
  const toast = document.createElement('div');
  const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
  toast.className = `toast px-4 py-2 rounded text-white shadow-lg ${bgColor}`;
  toast.textContent = message;
  toast.setAttribute('role', 'status');
  toast.setAttribute('aria-live', 'polite');
  container.appendChild(toast);
  setTimeout(() => toast.remove(), 4000);
}

export function formatError(err, fallbackMessage = 'An unexpected error occurred') {
  if (!err) return fallbackMessage;
  if (typeof err === 'string') return err;
  if (err.message) return err.message;
  return fallbackMessage;
}

export function safeAction(actionFn, { onError, finallyFn } = {}) {
  try {
    const res = actionFn();
    if (res && typeof res.then === 'function') {
      return res.catch(err => {
        onError?.(err);
        throw err;
      }).finally(() => {
        finallyFn?.();
      });
    }
    return res;
  } catch (err) {
    onError?.(err);
    throw err;
  } finally {
    if (!(actionFn && typeof actionFn.then === 'function')) {
      finallyFn?.();
    }
  }
}

export function reportError(context, err, code = ErrorCodes.UNKNOWN) {
  console.warn(`[${context}]`, code, err);
  showToast(`${context}: ${formatError(err)}`, 'error');
}
