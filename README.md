# HTML Previewer Pro

A powerful, enhanced HTML development environment with live preview, file management, and persistent storage capabilities.

## 🚀 Features

### ✨ Core Functionality
- **Live HTML Preview** - Real-time preview as you type
- **Syntax Highlighting** - Beautiful code highlighting with Prism.js
- **Persistent Storage** - Remembers your project state across sessions
- **Virtual File System** - Create and manage files without touching disk
- **Multi-file Support** - Work with multiple HTML files simultaneously

### 📁 File Management
- **Project Explorer** - Browse and manage your HTML files
- **New File Creation** - Quick file creation with templates
- **New Folder Creation** - Organize files in folders
- **File Import** - Drag & drop or import existing HTML files
- **Save All** - Save multiple modified files at once
- **Context Menu** - Right-click files for additional options

### 🎛️ Panel Management
- **Collapsible Panels** - Toggle file explorer, editor, and preview
- **Persistent Panel States** - Remembers panel visibility preferences
- **Improved Toggle System** - Toggle buttons remain accessible when collapsed

### ⌨️ Keyboard Shortcuts
- `Ctrl+N` - Create new file
- `Ctrl+S` - Save current file
- `Ctrl+Shift+S` - Save all modified files
- `Escape` - Close modals

### 🎨 User Experience
- **Dark/Light Mode** - Toggle between themes
- **File Status Indicators** - Visual indicators for saved/modified files
- **Toast Notifications** - User-friendly feedback messages
- **Console Integration** - View JavaScript errors and logs
- **Drag & Drop Support** - Import files by dragging

## 🛠️ Technical Implementation

### Architecture
- **Modular Design** - Separate classes for different functionality
- **State Management** - Centralized application state
- **Local Storage** - Persistent data storage
- **Virtual File System** - In-memory file management

### Key Components
- `FileManager` - Handles file operations and project management
- `EditorController` - Manages the code editor functionality
- `PreviewEngine` - Handles live preview and console capture
- `PanelManager` - Manages panel visibility and states
- `PersistenceManager` - Handles data persistence

### Storage Structure
```javascript
// localStorage keys used:
- htmlPreviewerDarkMode: boolean
- htmlPreviewerLastFolder: string
- htmlPreviewerPanelStates: object
- htmlPreviewerSession: object (complete session data)
```

## 📋 Usage

### Getting Started
1. Open `HTML_Previewer.html` in your web browser
2. Create a new file or select a project folder
3. Start coding with live preview
4. Your work is automatically saved to localStorage

### Creating New Files
1. Click the "+" button in the Project Explorer
2. Enter a filename (`.html` extension will be added automatically)
3. Start coding with the provided template

### Importing Files
1. Use the import button or drag & drop HTML files
2. Files are imported into the virtual file system
3. Save to download files to your computer

### Project Management
1. Select a folder to load existing HTML projects
2. Use the file tree to navigate between files
3. Right-click files for context menu options

## 🔧 Browser Requirements

- Modern browser with ES6+ support
- localStorage support required for persistence
- File API support for folder/file operations

## 🎯 Best Practices

1. **Regular Saving** - Use `Ctrl+S` to save individual files
2. **Project Organization** - Use folders to organize related files
3. **Auto-save** - The application auto-saves session data every 30 seconds
4. **Valid HTML** - The editor validates basic HTML structure before saving

## 🚧 Limitations

- **Save All** currently downloads files individually (ZIP functionality planned)
- **Rename** functionality is placeholder (coming soon)
- **Virtual files** exist only in browser storage until downloaded
- **File size limits** based on browser localStorage capacity

## 🔮 Future Enhancements

- Full ZIP download for Save All
- File rename functionality
- Advanced search and replace
- Code formatting and validation
- Plugin system for extensions
- Collaborative editing features

## 💡 Tips

- Use the console toggle to debug JavaScript in your HTML
- Panel states are remembered between sessions
- Files show visual indicators when modified
- Dark mode preference is persistent
- The application works offline once loaded

## 🐛 Troubleshooting

### Common Issues
1. **Files not loading** - Check browser console for errors
2. **Storage full** - Clear localStorage or export/save important files
3. **Panel stuck** - Refresh page to reset panel states
4. **Preview not updating** - Use the refresh preview button

### Browser Support
- Chrome/Edge: Full support
- Firefox: Full support  
- Safari: Full support
- IE: Not supported

---

**HTML Previewer Pro** - Your enhanced HTML development environment 