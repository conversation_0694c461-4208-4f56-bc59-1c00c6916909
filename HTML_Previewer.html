<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced HTML Previewer Pro</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&family=Fira+Code&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    <link href="styles/app.css" rel="stylesheet" />
</head>
<body class="bg-slate-50 dark:bg-slate-900 text-slate-900 dark:text-slate-50">
    <div id="app" class="flex h-screen w-screen text-sm">
        <!-- Files Pane -->
        <div id="files-pane" class="pane bg-white dark:bg-slate-800 border-r border-slate-200 dark:border-slate-700 flex flex-col" style="width: 300px; min-width: 220px; max-width: 50vw;">
            <div class="pane-content">
                <div class="p-2 border-b border-slate-200 dark:border-slate-700 flex justify-between items-center">
                    <h2 class="font-bold text-base">Project Explorer</h2>
                    <div class="flex items-center gap-0.5">
                        <button id="new-file-btn" title="New File (Ctrl+N)" class="p-1 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path></svg>
                        </button>
                        <button id="new-folder-btn" title="New Folder" class="p-1 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>
                        </button>
                        <button id="import-file-btn" title="Import File" class="p-1 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path></svg>
                        </button>
                    </div>
                </div>
                
                <div class="p-2">
                    <button id="select-folder-btn" class="w-full px-3 py-1.5 bg-blue-600 text-white rounded hover:bg-blue-700 transition text-sm">
                        Select Project Folder
                    </button>
                    <input type="file" id="folder-input" webkitdirectory directory multiple style="display: none;" />
                    <input type="file" id="import-input" accept=".html,.htm" style="display: none;" />
                </div>
                
                <div id="file-tree" class="flex-1 overflow-y-auto px-2 py-1">
                    <p class="text-slate-400 text-center py-3 px-2 text-sm">Select a folder to begin or create a new file.</p>
                </div>
                
                <div class="p-2 border-t border-slate-200 dark:border-slate-700">
                    <div id="root-path" class="text-xs text-slate-500 dark:text-slate-400 truncate mb-1.5">No project loaded</div>
                    <button id="save-all-btn" class="w-full px-3 py-1.5 bg-green-600 text-white rounded hover:bg-green-700 transition text-sm disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                        Save All (Ctrl+Shift+S)
                    </button>
                </div>
            </div>
            
            <div class="pane-toggle left" id="files-toggle">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </div>
        </div>

        <!-- Vertical Resizer between Files and Code -->
        <div class="resizer" id="resizer-files-code" role="separator" aria-label="Resize files and code panes" aria-orientation="vertical" tabindex="0"></div>

        <!-- Code Pane -->
        <div id="code-pane" class="pane flex flex-col border-r border-slate-200 dark:border-slate-700">
            <div class="pane-content flex flex-col h-full">
                <div class="p-3 border-b border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 flex justify-between items-center">
                    <div class="flex items-center">
                        <span id="file-status-indicator" class="status-indicator saved"></span>
                        <h1 id="file-status" class="text-base font-bold truncate pr-2">untitled.html</h1>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button id="save-btn" title="Save (Ctrl+S)" class="px-3 py-1.5 bg-blue-600 text-white rounded hover:bg-blue-700 transition text-sm">Save</button>
                        <div class="relative" id="search-wrap">
                            <button id="open-search-btn" title="Search & Replace (Ctrl+F)" class="p-2 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-4.35-4.35m1.6-4.65a6.25 6.25 0 11-12.5 0 6.25 6.25 0 0112.5 0z"></path>
                                </svg>
                            </button>
                        </div>
                        <button id="dark-mode-toggle" title="Toggle Dark Mode" class="p-2 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="flex-1 editor-container relative bg-white dark:bg-slate-900">
                    <textarea id="html-input" spellcheck="false" placeholder="Start typing your HTML code here..."></textarea>
                    <pre id="syntax-highlight" aria-hidden="true"><code class="language-markup"></code></pre>
                </div>
            </div>
            
            <div class="pane-toggle right" id="code-toggle">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </div>
        </div>

        <!-- Vertical Resizer between Code and Preview -->
        <div class="resizer" id="resizer-code-preview" role="separator" aria-label="Resize code and preview panes" aria-orientation="vertical" tabindex="0"></div>

        <!-- Preview Pane -->
        <div id="preview-pane" class="pane flex flex-col">
            <div class="pane-content flex flex-col h-full">
                <div class="p-3 border-b border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 flex justify-between items-center">
                    <h1 class="text-base font-bold">Live Preview</h1>
                    <div class="flex items-center space-x-2">
                        <button id="refresh-preview-btn" title="Refresh Preview" class="p-2 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                        </button>
                        <button id="toggle-console-btn" title="Toggle Console" class="p-2 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <iframe id="preview-output" class="flex-1 bg-white" sandbox="allow-scripts allow-same-origin"></iframe>
                <div id="console-output-container" class="h-32 bg-slate-900 text-white font-mono text-xs p-2 overflow-y-auto border-t border-slate-700 hidden">
                    <div class="font-bold text-slate-400 border-b border-slate-700 mb-1 pb-1">Preview Console</div>
                    <div id="console-output"></div>
                </div>
            </div>
            
            <div class="pane-toggle right" id="preview-toggle">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Context Menu -->
    <div id="context-menu" class="context-menu hidden">
        <div class="context-menu-item" data-action="open">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path></svg>
            Open
        </div>
        <div class="context-menu-item" data-action="rename">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path></svg>
            Rename
        </div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" data-action="delete">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
            Delete
        </div>
    </div>

    <!-- New File Modal -->
    <div id="new-file-modal" class="modal hidden">
        <div class="modal-content">
            <h3 class="text-lg font-bold mb-4">Create New File</h3>
            <input type="text" id="new-file-name" placeholder="filename.html" class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded mb-4 bg-white dark:bg-slate-700">
            <div class="flex justify-end space-x-2">
                <button id="cancel-new-file" class="px-4 py-2 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200">Cancel</button>
                <button id="create-new-file" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Create</button>
            </div>
        </div>
    </div>

    <!-- New Folder Modal -->
    <div id="new-folder-modal" class="modal hidden">
        <div class="modal-content">
            <h3 class="text-lg font-bold mb-4">Create New Folder</h3>
            <input type="text" id="new-folder-name" placeholder="folder-name" class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded mb-4 bg-white dark:bg-slate-700">
            <div class="flex justify-end space-x-2">
                <button id="cancel-new-folder" class="px-4 py-2 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200">Cancel</button>
                <button id="create-new-folder" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Create</button>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="delete-confirm-modal" class="modal hidden" role="dialog" aria-modal="true" aria-labelledby="delete-modal-title">
        <div class="modal-content">
            <h3 id="delete-modal-title" class="text-lg font-bold mb-2">Delete item</h3>
            <p id="delete-modal-message" class="text-sm text-slate-600 dark:text-slate-300 mb-4"></p>
            <div class="flex justify-between items-center mb-3">
                <div class="text-xs text-slate-500 dark:text-slate-400">
                    This removes the item from the current session. Loaded files must be saved manually to disk if needed.
                </div>
            </div>
            <div class="flex justify-end space-x-2">
                <button id="delete-cancel-btn" class="px-4 py-2 text-slate-700 dark:text-slate-300 rounded hover:bg-slate-200 dark:hover:bg-slate-600">Cancel</button>
                <button id="delete-confirm-btn" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">Delete</button>
            </div>
        </div>
    </div>

    <!-- Search & Replace Modal -->
    <div id="search-modal" class="modal hidden" role="dialog" aria-modal="true" aria-labelledby="search-modal-title">
        <div class="modal-content">
            <h3 id="search-modal-title" class="text-lg font-bold mb-3">Search & Replace</h3>
            <div class="space-y-3">
                <div>
                    <label class="block text-xs mb-1 text-slate-500 dark:text-slate-400">Find</label>
                    <input type="text" id="search-query" class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded bg-white dark:bg-slate-700" placeholder="Search term or regex">
                </div>
                <div>
                    <label class="block text-xs mb-1 text-slate-500 dark:text-slate-400">Replace</label>
                    <input type="text" id="search-replace" class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded bg-white dark:bg-slate-700" placeholder="Replacement (optional)">
                </div>
                <div class="flex items-center gap-4">
                    <label class="flex items-center gap-2 text-sm">
                        <input type="checkbox" id="search-regex">
                        <span>Regex</span>
                    </label>
                    <label class="flex items-center gap-2 text-sm">
                        <input type="checkbox" id="search-case">
                        <span>Match case</span>
                    </label>
                    <label class="flex items-center gap-2 text-sm">
                        <input type="checkbox" id="search-whole">
                        <span>Whole word</span>
                    </label>
                    <label class="flex items-center gap-2 text-sm">
                        <input type="checkbox" id="search-in-open-only">
                        <span>Current file only</span>
                    </label>
                </div>
                <div class="flex items-center gap-2">
                    <button id="search-run" class="px-3 py-1.5 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm">Search</button>
                    <button id="search-replace-one" class="px-3 py-1.5 bg-amber-600 text-white rounded hover:bg-amber-700 text-sm">Replace Selected</button>
                    <button id="search-replace-all" class="px-3 py-1.5 bg-green-600 text-white rounded hover:bg-green-700 text-sm">Replace All</button>
                    <button id="search-cancel" class="ml-auto px-3 py-1.5 rounded border border-slate-300 dark:border-slate-600 text-sm hover:bg-slate-100 dark:hover:bg-slate-700">Close</button>
                </div>
                <div id="search-results" class="max-h-56 overflow-y-auto border border-slate-200 dark:border-slate-700 rounded p-2 bg-white dark:bg-slate-800 text-xs"></div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-markup.min.js"></script>
    <!-- JSZip for proper ZIP creation in Save All -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js" integrity="sha512-0Jxv3dBC1wF8I5CwQO3Yp1Wq2p3lTg8b8wTj1H0kqLk0pJvXr7mJ7w8g7D3t4Wzj5e8xXhQmJvY0G3d0wq8d0w==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="scripts/app.js"></script>

            // ===== UTILITY FUNCTIONS =====
            function debounce(fn, wait) {
                let t;
                return (...args) => { clearTimeout(t); t = setTimeout(() => fn.apply(this, args), wait); };
            }

            function isBasicHtml(str) {
                return /<html[\s>]/i.test(str) && /<\/html>/i.test(str);
            }

            function showToast(message, type = 'info') {
                const container = document.getElementById('toast-container');
                const toast = document.createElement('div');
                const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
                toast.className = `toast px-4 py-2 rounded text-white shadow-lg ${bgColor}`;
                toast.textContent = message;
                // ARIA live region for accessibility
                toast.setAttribute('role', 'status');
                toast.setAttribute('aria-live', 'polite');
                container.appendChild(toast);
                setTimeout(() => toast.remove(), 4000);
            }

            function markDirty(dirty, filePath = state.currentFilePath) {
                if (filePath) {
                    if (dirty) {
                        state.modifiedFiles.add(filePath);
                    } else {
                        state.modifiedFiles.delete(filePath);
                    }
                }
                
                state.isDirty = dirty;
                updateFileStatus();
                updateSaveAllButton();
            }

            function updateFileStatus() {
                const fileName = state.currentFile?.name || state.currentFilePath?.split('/').pop() || 'untitled.html';
                dom.fileStatus.textContent = fileName;
                
                // Update status indicator
                if (state.isDirty) {
                    dom.fileStatusIndicator.className = 'status-indicator modified';
                } else {
                    dom.fileStatusIndicator.className = 'status-indicator saved';
                }
            }

            function updateSaveAllButton() {
                const hasModified = state.modifiedFiles.size > 0;
                dom.saveAllBtn.disabled = !hasModified;
                dom.saveAllBtn.textContent = hasModified ? `Save All (${state.modifiedFiles.size})` : 'Save All';
            }

            function saveSettings() {
                localStorage.setItem('htmlPreviewerDarkMode', state.settings.darkMode);
                localStorage.setItem('htmlPreviewerLastFolder', state.settings.lastFolder || '');
                localStorage.setItem('htmlPreviewerPanelStates', JSON.stringify(state.settings.panelStates));
                localStorage.setItem('htmlPreviewerPanelSizes', JSON.stringify(state.settings.panelSizes || {}));
            }

            function generateUniqueFileName(baseName = 'untitled', extension = '.html') {
                let counter = 1;
                let fileName = `${baseName}${extension}`;
                
                while (state.loadedFiles.has(fileName) || state.virtualFiles.has(fileName)) {
                    fileName = `${baseName}-${counter}${extension}`;
                    counter++;
                }
                
                return fileName;
            }

            // ===== PERSISTENCE MANAGER =====
            class PersistenceManager {
                static save() {
                    try {
                        const data = {
                            virtualFiles: Array.from(state.virtualFiles.entries()),
                            folders: Array.from(state.folders),
                            folderExpansion: Array.from(state.folderExpansion.entries()),
                            currentFile: state.currentFilePath,
                            editorContent: state.editorContent,
                            modifiedFiles: Array.from(state.modifiedFiles),
                            timestamp: Date.now()
                        };
                        localStorage.setItem('htmlPreviewerSession', JSON.stringify(data));
                    } catch (e) {
                        console.warn('Failed to save session:', e);
                    }
                }

                static restore() {
                    try {
                        const data = localStorage.getItem('htmlPreviewerSession');
                        if (!data) return false;

                        const session = JSON.parse(data);
                        
                        // Restore virtual files and folder states
                        state.virtualFiles = new Map(session.virtualFiles || []);
                        state.folders = new Set(session.folders || []);
                        state.folderExpansion = new Map(session.folderExpansion || []);
                        state.modifiedFiles = new Set(session.modifiedFiles || []);
                        
                        // Restore UI
                        fileManager.renderFileTree();
                        
                        // Restore current file if it exists
                        if (session.currentFile && (state.virtualFiles.has(session.currentFile) || state.loadedFiles.has(session.currentFile))) {
                            if (session.editorContent) {
                                editor.setContent(session.editorContent, state.modifiedFiles.has(session.currentFile));
                                state.currentFilePath = session.currentFile;
                                updateFileStatus();
                            }
                        }
                        
                        return true;
                    } catch (e) {
                        console.warn('Failed to restore session:', e);
                        return false;
                    }
                }
            }

            // ===== Error Handling Utilities =====
            const ErrorCodes = {
                INVALID_NAME: 'E_INVALID_NAME',
                DUPLICATE_PATH: 'E_DUPLICATE_PATH',
                NOT_FOUND: 'E_NOT_FOUND',
                READ_FAILED: 'E_READ_FAILED',
                ZIP_NOT_LOADED: 'E_ZIP_NOT_LOADED',
                NO_MODIFIED: 'E_NO_MODIFIED',
                INVALID_HTML: 'E_INVALID_HTML',
                ABORTED: 'E_ABORTED',
                UNKNOWN: 'E_UNKNOWN'
            };

            function formatError(err, fallbackMessage = 'An unexpected error occurred') {
                if (!err) return fallbackMessage;
                if (typeof err === 'string') return err;
                if (err.message) return err.message;
                return fallbackMessage;
            }

            function safeAction(actionFn, { onError, finallyFn } = {}) {
                try {
                    const res = actionFn();
                    if (res && typeof res.then === 'function') {
                        return res.catch(err => {
                            onError?.(err);
                            throw err;
                        }).finally(() => {
                            finallyFn?.();
                        });
                    }
                    return res;
                } catch (err) {
                    onError?.(err);
                    throw err;
                } finally {
                    if (! (actionFn && typeof actionFn.then === 'function')) {
                        finallyFn?.();
                    }
                }
            }

            function reportError(context, err, code = ErrorCodes.UNKNOWN) {
                console.warn(`[${context}]`, code, err);
                showToast(`${context}: ${formatError(err)}`, 'error');
            }

            // ===== FILE MANAGER =====
            class FileManager {
                constructor() {
                    this.setupEventListeners();
                }

                setupEventListeners() {
                    dom.selectFolderBtn.addEventListener('click', () => dom.folderInput.click());
                    dom.folderInput.addEventListener('change', (e) => this.handleFolderSelection(e.target.files));
                    
                    // New file/folder buttons
                    document.getElementById('new-file-btn').addEventListener('click', () => this.showNewFileModal());
                    document.getElementById('new-folder-btn').addEventListener('click', () => this.showNewFolderModal());
                    document.getElementById('import-file-btn').addEventListener('click', () => dom.importInput.click());
                    dom.importInput.addEventListener('change', (e) => this.handleFileImport(e.target.files));
                    
                    // Modal handlers
                    this.setupModalHandlers();
                    
                    // Context menu
                    this.setupContextMenu();
                }

                handleFolderSelection(files) {
                    if (!files || !files.length) {
                        showToast('No folder selected.', 'info');
                        return;
                    }

                    safeAction(() => {
                        state.loadedFiles.clear();
                        state.folders.clear();

                        Array.from(files).forEach(file => {
                            const path = file.webkitRelativePath;
                            if (!path) return;

                            const lastSlash = path.lastIndexOf('/');
                            const directory = lastSlash > -1 ? path.substring(0, lastSlash) : '';

                            if (directory) {
                                state.folders.add(directory);
                            }

                            if (file.name.toLowerCase().match(/\.(html|htm)$/)) {
                                state.loadedFiles.set(path, {
                                    file: file,
                                    isLoaded: false,
                                    content: null
                                });
                            }
                        });

                        const firstFilePath = files[0].webkitRelativePath || files[0].name;
                        const folderName = (firstFilePath?.split('/')?.[0]) || 'Project';
                        state.settings.lastFolder = folderName;
                        dom.rootPath.textContent = `Project: ${folderName}`;

                        this.renderFileTree();
                        saveSettings();
                        PersistenceManager.save();

                        showToast(`Loaded project: ${folderName}`, 'success');
                    }, {
                        onError: (err) => reportError('Load folder failed', err, ErrorCodes.READ_FAILED)
                    });
                }

                handleFileImport(files) {
                    if (!files || !files.length) {
                        showToast('No file selected for import.', 'info');
                        return;
                    }

                    const file = files[0];
                    if (!file.name.toLowerCase().match(/\.(html|htm)$/)) {
                        showToast('Only HTML files are supported for import.', 'error');
                        return;
                    }

                    const fileName = generateUniqueFileName(file.name.replace(/\.(html|htm)$/, ''), '.html');

                    safeAction(async () => {
                        const content = await file.text();
                        if (!content) {
                            throw new Error('File is empty or unreadable.');
                        }

                        state.virtualFiles.set(fileName, {
                            content: content,
                            isVirtual: true,
                            created: Date.now()
                        });

                        state.currentFile = null;
                        state.currentFilePath = fileName;
                        editor.setContent(content, true);
                        this.renderFileTree();
                        PersistenceManager.save();

                        showToast(`Imported ${fileName}`, 'success');
                    }, {
                        onError: (err) => reportError('Import file failed', err, ErrorCodes.READ_FAILED)
                    });
                }

                showNewFileModal() {
                    dom.newFileModal.classList.remove('hidden');
                    document.getElementById('new-file-name').focus();
                }

                showNewFolderModal() {
                    dom.newFolderModal.classList.remove('hidden');
                    document.getElementById('new-folder-name').focus();
                }

                setupModalHandlers() {
                    // New File Modal
                    document.getElementById('cancel-new-file').addEventListener('click', () => {
                        dom.newFileModal.classList.add('hidden');
                    });
                    
                    document.getElementById('create-new-file').addEventListener('click', () => {
                        const name = document.getElementById('new-file-name').value.trim();
                        if (name) {
                            this.createNewFile(name);
                            dom.newFileModal.classList.add('hidden');
                            document.getElementById('new-file-name').value = '';
                        }
                    });
                    
                    document.getElementById('new-file-name').addEventListener('keydown', (e) => {
                        if (e.key === 'Enter') {
                            document.getElementById('create-new-file').click();
                        } else if (e.key === 'Escape') {
                            document.getElementById('cancel-new-file').click();
                        }
                    });

                    // New Folder Modal
                    document.getElementById('cancel-new-folder').addEventListener('click', () => {
                        dom.newFolderModal.classList.add('hidden');
                    });
                    
                    document.getElementById('create-new-folder').addEventListener('click', () => {
                        const name = document.getElementById('new-folder-name').value.trim();
                        if (name) {
                            this.createNewFolder(name);
                            dom.newFolderModal.classList.add('hidden');
                            document.getElementById('new-folder-name').value = '';
                        }
                    });
                    
                    document.getElementById('new-folder-name').addEventListener('keydown', (e) => {
                        if (e.key === 'Enter') {
                            document.getElementById('create-new-folder').click();
                        } else if (e.key === 'Escape') {
                            document.getElementById('cancel-new-folder').click();
                        }
                    });

                    // Close modals when clicking outside
                    [dom.newFileModal, dom.newFolderModal].forEach(modal => {
                        modal.addEventListener('click', (e) => {
                            if (e.target === modal) {
                                modal.classList.add('hidden');
                            }
                        });
                    });

                    // Delete confirm modal handlers
                    const deleteModal = document.getElementById('delete-confirm-modal');
                    const deleteCancelBtn = document.getElementById('delete-cancel-btn');
                    const deleteConfirmBtn = document.getElementById('delete-confirm-btn');

                    deleteCancelBtn.addEventListener('click', () => {
                        deleteModal.classList.add('hidden');
                        this._pendingDelete = null;
                    });
                    deleteConfirmBtn.addEventListener('click', () => {
                        if (this._pendingDelete) {
                            this._performDelete(this._pendingDelete);
                            this._pendingDelete = null;
                        }
                        deleteModal.classList.add('hidden');
                    });
                    deleteModal.addEventListener('keydown', (e) => {
                        if (e.key === 'Escape') {
                            e.preventDefault();
                            deleteCancelBtn.click();
                        } else if (e.key === 'Enter') {
                            e.preventDefault();
                            deleteConfirmBtn.click();
                        }
                    });
                    deleteModal.addEventListener('click', (e) => {
                        if (e.target === deleteModal) {
                            deleteCancelBtn.click();
                        }
                    });
                }

                createNewFile(fileName) {
                    if (!fileName.toLowerCase().endsWith('.html')) {
                        fileName += '.html';
                    }
                    
                    const uniqueName = generateUniqueFileName(fileName.replace('.html', ''), '.html');
                    const defaultContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${uniqueName.replace('.html', '')}</title>
</head>
<body>
    <h1>Welcome to ${uniqueName}</h1>
    <p>Start building your HTML page here!</p>
</body>
</html>`;

                    state.virtualFiles.set(uniqueName, {
                        content: defaultContent,
                        isVirtual: true,
                        created: Date.now()
                    });
                    
                    state.currentFile = null;
                    state.currentFilePath = uniqueName;
                    editor.setContent(defaultContent, true);
                    this.renderFileTree();
                    PersistenceManager.save();
                    
                    showToast(`Created ${uniqueName}`, 'success');
                }

                createNewFolder(folderName) {
                    state.folders.add(folderName);
                    this.renderFileTree();
                    PersistenceManager.save();
                    showToast(`Created folder: ${folderName}`, 'success');
                }

                setupContextMenu() {
                    document.addEventListener('click', () => {
                        dom.contextMenu.classList.add('hidden');
                    });

                    dom.contextMenu.addEventListener('click', (e) => {
                        e.stopPropagation();
                        const action = e.target.closest('[data-action]')?.dataset.action;
                        if (action) {
                            this.handleContextAction(action);
                            dom.contextMenu.classList.add('hidden');
                        }
                    });
                }

                showContextMenu(e, filePath) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    this.contextTarget = filePath;
                    dom.contextMenu.style.left = e.pageX + 'px';
                    dom.contextMenu.style.top = e.pageY + 'px';
                    dom.contextMenu.classList.remove('hidden');
                }

                handleContextAction(action) {
                    const filePath = this.contextTarget;
                    if (!filePath) return;

                    switch (action) {
                        case 'open':
                            this.loadFile(filePath);
                            break;
                        case 'rename':
                            this.renameFile(filePath);
                            break;
                        case 'delete':
                            this.deleteFile(filePath);
                            break;
                    }
                }

                renameFile(filePath) {
                    const isFolder = [...state.folders].some(f => f === filePath || f.startsWith(filePath + '/'));
                    const currentName = filePath.split('/').pop();
                    const parentPath = filePath.includes('/') ? filePath.slice(0, filePath.lastIndexOf('/')) : '';

                    const newName = prompt('Enter new name:', currentName);
                    if (newName === null) {
                        // user cancelled
                        return;
                    }

                    const trimmed = (newName || '').trim();
                    if (!trimmed) {
                        showToast('Name cannot be empty.', 'error');
                        return;
                    }
                    if (trimmed === currentName) {
                        return;
                    }

                    // Basic sanitization and validation
                    const invalidChars = /[\\:*?"<>|]/;
                    if (invalidChars.test(trimmed)) {
                        reportError('Rename failed', new Error('Invalid name. Avoid \\ / : * ? " < > |'), ErrorCodes.INVALID_NAME);
                        return;
                    }

                    const newPath = parentPath ? `${parentPath}/${trimmed}` : trimmed;

                    safeAction(() => {
                        if (isFolder) {
                            const updatedFolders = new Set();
                            state.folders.forEach(f => {
                                if (f === filePath || f.startsWith(filePath + '/')) {
                                    const renamed = f.replace(filePath, newPath);
                                    updatedFolders.add(renamed);
                                } else {
                                    updatedFolders.add(f);
                                }
                            });
                            state.folders = updatedFolders;

                            const remapMapKeys = (m) => {
                                const entries = Array.from(m.entries());
                                m.clear();
                                entries.forEach(([k, v]) => {
                                    if (k.startsWith(filePath + '/')) {
                                        const renamedKey = k.replace(filePath + '/', newPath + '/');
                                        m.set(renamedKey, v);
                                    } else {
                                        m.set(k, v);
                                    }
                                });
                            };
                            remapMapKeys(state.loadedFiles);
                            remapMapKeys(state.virtualFiles);

                            const newModified = new Set();
                            state.modifiedFiles.forEach(p => {
                                if (p.startsWith(filePath + '/')) {
                                    newModified.add(p.replace(filePath + '/', newPath + '/'));
                                } else {
                                    newModified.add(p);
                                }
                            });
                            state.modifiedFiles = newModified;

                            if (state.currentFilePath && state.currentFilePath.startsWith(filePath + '/')) {
                                state.currentFilePath = state.currentFilePath.replace(filePath + '/', newPath + '/');
                            }

                            this.renderFileTree();
                            PersistenceManager.save();
                            showToast(`Renamed folder to ${trimmed}`, 'success');
                            return;
                        }

                        // Rename a single file
                        const existsInLoaded = state.loadedFiles.has(newPath);
                        const existsInVirtual = state.virtualFiles.has(newPath);
                        if (existsInLoaded || existsInVirtual) {
                            throw Object.assign(new Error('A file with that name already exists.'), { code: ErrorCodes.DUPLICATE_PATH });
                        }

                        if (state.virtualFiles.has(filePath)) {
                            const meta = state.virtualFiles.get(filePath);
                            state.virtualFiles.delete(filePath);
                            state.virtualFiles.set(newPath, meta);
                        }
                        if (state.loadedFiles.has(filePath)) {
                            const meta = state.loadedFiles.get(filePath);
                            state.loadedFiles.delete(filePath);
                            state.loadedFiles.set(newPath, meta);
                        }

                        if (state.currentFilePath === filePath) {
                            state.currentFilePath = newPath;
                        }

                        if (state.modifiedFiles.has(filePath)) {
                            state.modifiedFiles.delete(filePath);
                            state.modifiedFiles.add(newPath);
                        }

                        this.renderFileTree();
                        PersistenceManager.save();
                        showToast(`Renamed to ${trimmed}`, 'success');
                    }, {
                        onError: (err) => reportError('Rename failed', err, err.code || ErrorCodes.UNKNOWN)
                    });
                }

                deleteFile(filePath) {
                    const name = filePath.split('/').pop();
                    const isFolder = [...state.folders].some(f => f === filePath || f.startsWith(filePath + '/'));

                    // Populate and show modal
                    const deleteModal = document.getElementById('delete-confirm-modal');
                    const msgEl = document.getElementById('delete-modal-message');
                    const typeLabel = isFolder ? 'folder' : 'file';
                    msgEl.textContent = `Are you sure you want to delete the ${typeLabel} "${name}"? This cannot be undone in this session.`;
                    deleteModal.classList.remove('hidden');

                    // Track pending delete target
                    this._pendingDelete = { filePath, isFolder };
                }

                _performDelete(target) {
                    const { filePath, isFolder } = target;
                    const name = filePath.split('/').pop();

                    safeAction(() => {
                        if (isFolder) {
                            // Delete folder content from sets/maps
                            const newFolders = new Set();
                            state.folders.forEach(f => {
                                if (!(f === filePath || f.startsWith(filePath + '/'))) {
                                    newFolders.add(f);
                                }
                            });
                            state.folders = newFolders;

                            const deleteByPrefix = (m) => {
                                for (const key of Array.from(m.keys())) {
                                    if (key === filePath || key.startsWith(filePath + '/')) {
                                        m.delete(key);
                                    }
                                }
                            };
                            deleteByPrefix(state.virtualFiles);
                            deleteByPrefix(state.loadedFiles);

                            for (const key of Array.from(state.modifiedFiles)) {
                                if (key === filePath || key.startsWith(filePath + '/')) {
                                    state.modifiedFiles.delete(key);
                                }
                            }

                            if (state.currentFilePath && (state.currentFilePath === filePath || state.currentFilePath.startsWith(filePath + '/'))) {
                                const remainingFiles = [...state.virtualFiles.keys(), ...state.loadedFiles.keys()];
                                if (remainingFiles.length > 0) {
                                    this.loadFile(remainingFiles[0]);
                                } else {
                                    this.createNewFile('untitled');
                                }
                            }

                            this.renderFileTree();
                            PersistenceManager.save();
                            showToast(`Deleted folder "${name}"`, 'success');
                            return;
                        }

                        if (!state.virtualFiles.has(filePath) && !state.loadedFiles.has(filePath)) {
                            throw Object.assign(new Error('Item not found.'), { code: ErrorCodes.NOT_FOUND });
                        }

                        if (state.virtualFiles.has(filePath)) {
                            state.virtualFiles.delete(filePath);
                        }
                        if (state.loadedFiles.has(filePath)) {
                            state.loadedFiles.delete(filePath);
                        }
                        state.modifiedFiles.delete(filePath);

                        if (state.currentFilePath === filePath) {
                            const remainingFiles = [...state.virtualFiles.keys(), ...state.loadedFiles.keys()];
                            if (remainingFiles.length > 0) {
                                this.loadFile(remainingFiles[0]);
                            } else {
                                this.createNewFile('untitled');
                            }
                        }

                        this.renderFileTree();
                        PersistenceManager.save();
                        showToast(`Deleted "${name}"`, 'success');
                    }, {
                        onError: (err) => reportError('Delete failed', err, err.code || ErrorCodes.UNKNOWN)
                    });
                }

                renderFileTree() {
                    dom.fileTree.innerHTML = '';
                    
                    const hasFiles = state.loadedFiles.size > 0 || state.virtualFiles.size > 0;
                    const hasFolders = state.folders.size > 0;
                    
                    if (!hasFiles && !hasFolders) {
                        dom.fileTree.innerHTML = '<p class="text-slate-400 text-center p-4 text-sm">Select a folder to begin or create a new file.</p>';
                        return;
                    }

                    // Build tree structure
                    const tree = this.buildTreeStructure();
                    
                    // Render tree
                    this.renderTreeNode(tree, dom.fileTree, 0);
                }

                buildTreeStructure() {
                    const tree = { children: {}, files: [] };
                    
                    // Add folders to tree
                    state.folders.forEach(folderPath => {
                        this.addPathToTree(tree, folderPath, 'folder');
                    });
                    
                    // Add files to tree
                    const allFiles = [
                        ...Array.from(state.loadedFiles.keys()).map(path => ({ path, type: 'loaded' })),
                        ...Array.from(state.virtualFiles.keys()).map(path => ({ path, type: 'virtual' }))
                    ];
                    
                    allFiles.forEach(({ path, type }) => {
                        this.addPathToTree(tree, path, 'file', type);
                    });
                    
                    return tree;
                }

                addPathToTree(tree, path, itemType, fileType = null) {
                    const parts = path.split('/').filter(part => part.length > 0);
                    let current = tree;
                    
                    // Navigate/create folder structure
                    for (let i = 0; i < parts.length - (itemType === 'file' ? 1 : 0); i++) {
                        const part = parts[i];
                        if (!current.children[part]) {
                            current.children[part] = { 
                                children: {}, 
                                files: [],
                                name: part,
                                fullPath: parts.slice(0, i + 1).join('/'),
                                type: 'folder'
                            };
                        }
                        current = current.children[part];
                    }
                    
                    // Add file to appropriate folder
                    if (itemType === 'file') {
                        const fileName = parts[parts.length - 1];
                        current.files.push({
                            name: fileName,
                            fullPath: path,
                            type: 'file',
                            fileType: fileType
                        });
                    }
                }

                renderTreeNode(node, container, depth) {
                    // Sort folders and files
                    const sortedFolders = Object.keys(node.children).sort();
                    const sortedFiles = node.files.sort((a, b) => a.name.localeCompare(b.name));
                    
                    // Render folders first
                    sortedFolders.forEach(folderName => {
                        const folderNode = node.children[folderName];
                        const folderElement = this.createTreeFolderElement(folderNode, depth);
                        container.appendChild(folderElement);
                        
                        // Create children container
                        const childrenContainer = document.createElement('div');
                        childrenContainer.className = 'tree-children';
                        childrenContainer.id = `folder-${folderNode.fullPath.replace(/[^a-zA-Z0-9]/g, '-')}`;
                        
                        // Check if folder should be expanded
                        const isExpanded = state.folderExpansion.get(folderNode.fullPath) !== false; // Default to expanded
                        if (!isExpanded) {
                            childrenContainer.classList.add('collapsed');
                        }
                        
                        container.appendChild(childrenContainer);
                        
                        // Recursively render children
                        this.renderTreeNode(folderNode, childrenContainer, depth + 1);
                    });
                    
                    // Then render files
                    sortedFiles.forEach(file => {
                        const fileElement = this.createTreeFileElement(file, depth);
                        container.appendChild(fileElement);
                    });
                }

                createTreeFolderElement(folderNode, depth) {
                    const div = document.createElement('div');
                    div.className = 'tree-item folder-item';
                    div.setAttribute('data-depth', depth);
                    
                    const isExpanded = state.folderExpansion.get(folderNode.fullPath) !== false;
                    
                    const hasChildren = Object.keys(folderNode.children).length > 0 || folderNode.files.length > 0;
                    
                    div.innerHTML = `
                        ${hasChildren ? `
                            <div class="folder-toggle ${isExpanded ? 'expanded' : ''}">
                                <svg fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        ` : '<div class="folder-toggle"></div>'}
                        <svg class="mr-2 flex-shrink-0" viewBox="0 0 24 24" width="20" height="20" fill="#10b981">
                            <path d="M3 6a2 2 0 0 1 2-2h5l2 2h7a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V6z"></path>
                        </svg>
                        <span class="truncate font-medium">${folderNode.name}</span>
                    `;
                    
                    // Add click handler for expansion
                    if (hasChildren) {
                        div.addEventListener('click', (e) => {
                            e.stopPropagation();
                            this.toggleFolder(folderNode.fullPath);
                        });
                    }
                    
                    return div;
                }

                createTreeFileElement(file, depth) {
                    const div = document.createElement('div');
                    const isActive = state.currentFilePath === file.fullPath;
                    const isModified = state.modifiedFiles.has(file.fullPath);
                    
                    let className = 'tree-item file-item';
                    if (isActive) className += ' active';
                    if (isModified) className += ' modified';
                    
                    div.className = className;
                    div.setAttribute('data-depth', depth);
                    
                    const ext = (file.name.split('.').pop() || '').toLowerCase();
                    const color = ext === 'html' ? '#3b82f6' :
                                  ext === 'htm'  ? '#3b82f6' :
                                  ext === 'css'  ? '#22c55e' :
                                  ext === 'js'   ? '#f59e0b' :
                                  ext === 'ts'   ? '#2563eb' :
                                  ext === 'json' ? '#10b981' :
                                  ext === 'py'   ? '#0ea5e9' :
                                  ext === 'txt'  ? '#64748b' :
                                  '#3b82f6';
                    const label = (ext || 'file').toUpperCase();
                    const icon = `
                        <svg class="mr-2 flex-shrink-0" viewBox="0 0 24 24" width="18" height="18" aria-hidden="true">
                            <path fill="${color}" d="M6 2h7l5 5v13a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z"></path>
                            <path fill="white" d="M13 2v5h5"></path>
                            <text x="12" y="18" text-anchor="middle" font-size="7" font-family="Inter, system-ui, -apple-system, Segoe UI, Roboto, sans-serif" fill="white" font-weight="700">${label}</text>
                        </svg>
                    `;
                    
                    div.innerHTML = `
                        <div class="folder-toggle"></div>
                        ${icon}
                        <span class="truncate">${file.name}</span>
                    `;
                    
                    div.addEventListener('click', () => this.loadFile(file.fullPath));
                    div.addEventListener('contextmenu', (e) => this.showContextMenu(e, file.fullPath));
                    
                    return div;
                }

                toggleFolder(folderPath) {
                    const isExpanded = state.folderExpansion.get(folderPath) !== false;
                    state.folderExpansion.set(folderPath, !isExpanded);
                    
                    // Update UI
                    const folderId = `folder-${folderPath.replace(/[^a-zA-Z0-9]/g, '-')}`;
                    const childrenContainer = document.getElementById(folderId);
                    const folderElement = childrenContainer?.previousElementSibling;
                    
                    if (childrenContainer && folderElement) {
                        const toggle = folderElement.querySelector('.folder-toggle');
                        
                        if (!isExpanded) {
                            // Expand
                            childrenContainer.classList.remove('collapsed');
                            toggle?.classList.add('expanded');
                        } else {
                            // Collapse
                            childrenContainer.classList.add('collapsed');
                            toggle?.classList.remove('expanded');
                        }
                    }
                    
                    // Save state
                    PersistenceManager.save();
                }



                async loadFile(filePath) {
                    if (state.isDirty && !confirm('You have unsaved changes. Continue without saving?')) {
                        return;
                    }

                    return safeAction(async () => {
                        if (!filePath) {
                            throw new Error('No file specified.');
                        }

                        let content = '';

                        if (state.virtualFiles.has(filePath)) {
                            content = state.virtualFiles.get(filePath).content ?? '';
                        } else if (state.loadedFiles.has(filePath)) {
                            const fileData = state.loadedFiles.get(filePath);
                            if (!fileData) {
                                throw Object.assign(new Error('File metadata missing.'), { code: ErrorCodes.NOT_FOUND });
                            }
                            if (!fileData.isLoaded) {
                                content = await fileData.file.text();
                                fileData.content = content;
                                fileData.isLoaded = true;
                            } else {
                                content = fileData.content ?? '';
                            }
                        } else {
                            throw Object.assign(new Error('File not found.'), { code: ErrorCodes.NOT_FOUND });
                        }

                        state.currentFile = state.loadedFiles.get(filePath)?.file || null;
                        state.currentFilePath = filePath;
                        editor.setContent(content, state.modifiedFiles.has(filePath));
                        this.renderFileTree();
                        PersistenceManager.save();

                        showToast(`Loaded ${filePath.split('/').pop()}`, 'info');
                    }, {
                        onError: (err) => reportError('Load file failed', err, err.code || ErrorCodes.READ_FAILED)
                    });
                }

                saveCurrentFile() {
                    if (!state.currentFilePath) {
                        showToast('No file to save', 'error');
                        return;
                    }

                    const content = state.editorContent;
                    if (!isBasicHtml(content)) {
                        const proceed = confirm('Content may not be valid HTML. Save anyway?');
                        if (!proceed) return;
                    }

                    safeAction(() => {
                        const fileName = state.currentFilePath.split('/').pop();
                        const blob = new Blob([content], { type: 'text/html' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = fileName;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);

                        if (state.virtualFiles.has(state.currentFilePath)) {
                            state.virtualFiles.get(state.currentFilePath).content = content;
                        }

                        markDirty(false);
                        this.renderFileTree();
                        PersistenceManager.save();
                        showToast(`Saved ${fileName}`, 'success');
                    }, {
                        onError: (err) => reportError('Save failed', err)
                    });
                }

                async saveAllFiles() {
                    return safeAction(async () => {
                        if (state.modifiedFiles.size === 0) {
                            throw Object.assign(new Error('No modified files to save.'), { code: ErrorCodes.NO_MODIFIED });
                        }

                        if (typeof JSZip === 'undefined') {
                            throw Object.assign(new Error('ZIP library not loaded. Please check your internet connection.'), { code: ErrorCodes.ZIP_NOT_LOADED });
                        }

                        const zip = new JSZip();

                        const addToZip = (path, content) => {
                            const normalized = (path || '').replace(/^\/+/, '');
                            zip.file(normalized, content != null ? content : '');
                        };

                        for (const filePath of state.modifiedFiles) {
                            if (state.virtualFiles.has(filePath)) {
                                const vf = state.virtualFiles.get(filePath);
                                addToZip(filePath, vf?.content ?? '');
                            }
                        }

                        for (const filePath of state.modifiedFiles) {
                            if (state.loadedFiles.has(filePath)) {
                                const lf = state.loadedFiles.get(filePath);
                                const content = (filePath === state.currentFilePath) ? state.editorContent : (lf?.content ?? '');
                                addToZip(filePath, content);
                            }
                        }

                        if (!Object.keys(zip.files || {}).length) {
                            throw new Error('No content available to zip.');
                        }

                        const blob = await zip.generateAsync({ type: 'blob' });
                        const a = document.createElement('a');
                        a.href = URL.createObjectURL(blob);
                        a.download = `project-${Date.now()}.zip`;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(a.href);

                        state.modifiedFiles.clear();
                        state.isDirty = false;
                        this.renderFileTree();
                        updateFileStatus();
                        updateSaveAllButton();
                        PersistenceManager.save();

                        showToast('Project saved as a zip file!', 'success');
                    }, {
                        onError: (err) => reportError('Save All failed', err, err.code || ErrorCodes.UNKNOWN)
                    });
                }

                createZipFile() {
                    // Deprecated placeholder. ZIP creation now handled directly in saveAllFiles() with JSZip.
                    return '';
                }
            }

            // ===== SEARCH MANAGER =====
            class SearchManager {
                constructor() {
                    this.results = [];
                    this.selectedIndex = -1;
                    this._bindUI();
                }

                _bindUI() {
                    const modal = document.getElementById('search-modal');
                    const openBtn = document.getElementById('open-search-btn');
                    const cancelBtn = document.getElementById('search-cancel');
                    const runBtn = document.getElementById('search-run');
                    const repOneBtn = document.getElementById('search-replace-one');
                    const repAllBtn = document.getElementById('search-replace-all');

                    openBtn.addEventListener('click', () => this.open());
                    cancelBtn.addEventListener('click', () => this.close());
                    modal.addEventListener('click', (e) => { if (e.target === modal) this.close(); });

                    runBtn.addEventListener('click', () => this.search());
                    repOneBtn.addEventListener('click', () => this.replaceOne());
                    repAllBtn.addEventListener('click', () => this.replaceAll());

                    // Keyboard shortcut: Ctrl+F to open, Enter to search inside modal, Esc to close
                    window.addEventListener('keydown', (e) => {
                        const mod = e.metaKey || e.ctrlKey;
                        if (mod && e.key.toLowerCase() === 'f') {
                            e.preventDefault();
                            this.open();
                        }
                    });

                    modal.addEventListener('keydown', (e) => {
                        if (e.key === 'Escape') {
                            e.preventDefault();
                            this.close();
                        } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                            e.preventDefault();
                            this.search();
                        }
                    });
                }

                open() {
                    document.getElementById('search-modal').classList.remove('hidden');
                    document.getElementById('search-query').focus();
                    this.renderResults([]);
                }

                close() {
                    document.getElementById('search-modal').classList.add('hidden');
                }

                buildMatcher(query, { regex, matchCase, wholeWord }) {
                    if (!regex) {
                        // Escape regex special chars if not using regex mode
                        const escaped = query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                        query = escaped;
                    }
                    if (wholeWord) {
                        query = `\\b${query}\\b`;
                    }
                    try {
                        return new RegExp(query, matchCase ? 'g' : 'gi');
                    } catch (e) {
                        reportError('Invalid search pattern', e);
                        return null;
                    }
                }

                collectFiles(currentOnly) {
                    if (currentOnly) {
                        if (!state.currentFilePath) return [];
                        const content = this._getFileContent(state.currentFilePath);
                        return [{ path: state.currentFilePath, content }];
                    }

                    const paths = new Set([
                        ...state.virtualFiles.keys(),
                        ...state.loadedFiles.keys()
                    ]);
                    const files = [];
                    paths.forEach(p => {
                        files.push({ path: p, content: this._getFileContent(p) });
                    });
                    return files;
                }

                _getFileContent(path) {
                    if (path === state.currentFilePath) {
                        return state.editorContent ?? '';
                    }
                    if (state.virtualFiles.has(path)) {
                        return state.virtualFiles.get(path)?.content ?? '';
                    }
                    if (state.loadedFiles.has(path)) {
                        const lf = state.loadedFiles.get(path);
                        return lf?.content ?? '';
                    }
                    return '';
                }

                search() {
                    const query = document.getElementById('search-query').value || '';
                    const opts = {
                        regex: document.getElementById('search-regex').checked,
                        matchCase: document.getElementById('search-case').checked,
                        wholeWord: document.getElementById('search-whole').checked,
                        currentOnly: document.getElementById('search-in-open-only').checked
                    };

                    if (!query) {
                        this.renderResults([]);
                        showToast('Enter a search query', 'info');
                        return;
                    }

                    const matcher = this.buildMatcher(query, opts);
                    if (!matcher) return;

                    const files = this.collectFiles(opts.currentOnly);
                    const results = [];
                    files.forEach(({ path, content }) => {
                        if (!content) return;
                        let m;
                        // reset lastIndex for global regex reuse
                        matcher.lastIndex = 0;
                        while ((m = matcher.exec(content)) !== null) {
                            const idx = m.index;
                            const lineInfo = this._extractLine(content, idx, m[0].length);
                            results.push({
                                path,
                                index: idx,
                                match: m[0],
                                line: lineInfo.line,
                                lineNo: lineInfo.lineNo,
                                col: lineInfo.col
                            });
                            if (m[0].length === 0) {
                                matcher.lastIndex++;
                            }
                        }
                    });

                    this.results = results;
                    this.selectedIndex = results.length ? 0 : -1;
                    this.renderResults(results);
                    showToast(`${results.length} match(es) found`, 'info');
                }

                _extractLine(content, index, len) {
                    const before = content.slice(0, index);
                    const lineNo = (before.match(/\n/g) || []).length + 1;
                    const lineStart = before.lastIndexOf('\n') + 1;
                    const lineEnd = content.indexOf('\n', index);
                    const line = content.slice(lineStart, lineEnd === -1 ? content.length : lineEnd);
                    const col = index - lineStart + 1;
                    return { line, lineNo, col };
                }

                renderResults(results) {
                    const container = document.getElementById('search-results');
                    container.innerHTML = '';
                    if (!results.length) {
                        container.innerHTML = '<div class="text-slate-400">No results</div>';
                        return;
                    }

                    results.forEach((r, i) => {
                        const item = document.createElement('div');
                        item.className = 'p-2 rounded hover:bg-slate-100 dark:hover:bg-slate-700 cursor-pointer flex flex-col gap-0.5';
                        item.innerHTML = `
                            <div class="flex items-center gap-2">
                                <span class="text-[10px] px-1 py-0.5 bg-slate-200 dark:bg-slate-700 rounded">${r.path.split('/').pop()}</span>
                                <span class="text-slate-500">L${r.lineNo}:C${r.col}</span>
                            </div>
                            <div class="font-mono text-xs truncate">${this._highlightSnippet(r.line, r.match)}</div>
                        `;
                        item.addEventListener('click', () => this.jumpToResult(i));
                        container.appendChild(item);
                    });
                }

                _highlightSnippet(line, match) {
                    const safe = (s) => s.replace(/[&<>]/g, c => ({'&':'&','<':'<','>':'>'}[c]));
                    const idx = line.toLowerCase().indexOf(match.toLowerCase());
                    if (idx === -1) return safe(line);
                    return safe(line.slice(0, idx)) + '<span class="bg-yellow-300 text-black px-0.5 rounded">' + safe(line.slice(idx, idx + match.length)) + '</span>' + safe(line.slice(idx + match.length));
                }

                jumpToResult(i) {
                    if (i < 0 || i >= this.results.length) return;
                    const r = this.results[i];
                    fileManager.loadFile(r.path).then(() => {
                        // place caret near index
                        editor.setSelection(r.index, r.index + r.match.length);
                        this.selectedIndex = i;
                    });
                }

                replaceOne() {
                    if (this.selectedIndex < 0) {
                        showToast('Select a result first', 'info');
                        return;
                    }
                    const replacement = document.getElementById('search-replace').value ?? '';
                    const r = this.results[this.selectedIndex];
                    const content = this._getFileContent(r.path);
                    const newContent = content.slice(0, r.index) + replacement + content.slice(r.index + r.match.length);

                    this._applyContent(r.path, newContent, true);
                    showToast('Replaced one occurrence', 'success');
                    // Rerun search to refresh positions
                    this.search();
                }

                replaceAll() {
                    if (!this.results.length) {
                        showToast('No results to replace', 'info');
                        return;
                    }
                    const replacement = document.getElementById('search-replace').value ?? '';
                    const query = document.getElementById('search-query').value || '';
                    const opts = {
                        regex: document.getElementById('search-regex').checked,
                        matchCase: document.getElementById('search-case').checked,
                        wholeWord: document.getElementById('search-whole').checked,
                        currentOnly: document.getElementById('search-in-open-only').checked
                    };
                    const matcher = this.buildMatcher(query, opts);
                    if (!matcher) return;

                    const files = this.collectFiles(opts.currentOnly);
                    let replaceCount = 0;

                    files.forEach(({ path, content }) => {
                        if (!content) return;
                        matcher.lastIndex = 0;
                        const newContent = content.replace(matcher, (m) => { replaceCount++; return replacement; });
                        if (newContent !== content) {
                            this._applyContent(path, newContent, true);
                        }
                    });

                    this.search();
                    showToast(`Replaced ${replaceCount} occurrence(s)`, 'success');
                }

                _applyContent(path, content, markModified) {
                    if (path === state.currentFilePath) {
                        editor.setContent(content, markModified);
                    } else if (state.virtualFiles.has(path)) {
                        const vf = state.virtualFiles.get(path);
                        vf.content = content;
                        if (markModified) markDirty(true, path);
                    } else if (state.loadedFiles.has(path)) {
                        const lf = state.loadedFiles.get(path);
                        lf.content = content;
                        lf.isLoaded = true;
                        if (markModified) markDirty(true, path);
                    }
                    fileManager.renderFileTree();
                    PersistenceManager.save();
                }
            }

            // ===== EDITOR CONTROLLER =====
            class EditorController {
                constructor() {
                    this._updateSyntaxHighlight = this._updateSyntaxHighlight.bind(this);
                    this.debouncedHighlight = debounce(this._updateSyntaxHighlight, 150);
                }

                init() {
                    dom.htmlInput.addEventListener('input', () => {
                        state.editorContent = dom.htmlInput.value;
                        
                        // Update virtual file content
                        if (state.currentFilePath && state.virtualFiles.has(state.currentFilePath)) {
                            state.virtualFiles.get(state.currentFilePath).content = state.editorContent;
                        }
                        
                        markDirty(true);
                        this.debouncedHighlight();
                        preview.scheduleUpdate(state.editorContent);
                        PersistenceManager.save();
                    });

                    dom.htmlInput.addEventListener('scroll', () => {
                        dom.syntaxHighlight.scrollTop = dom.htmlInput.scrollTop;
                        dom.syntaxHighlight.scrollLeft = dom.htmlInput.scrollLeft;
                    });

                    // Initialize with default content
                    const defaultContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to HTML Previewer Pro</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .welcome { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; text-align: center; }
        .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 30px; }
        .feature { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; }
    </style>
</head>
<body>
    <div class="welcome">
        <h1>🚀 HTML Previewer Pro</h1>
        <p>Your enhanced HTML development environment</p>
    </div>
    
    <div class="features">
        <div class="feature">
            <h3>✨ New Features</h3>
            <ul>
                <li>Persistent project state</li>
                <li>Virtual file system</li>
                <li>Enhanced file management</li>
                <li>Improved collapse panels</li>
            </ul>
        </div>
        
        <div class="feature">
            <h3>🎯 Quick Actions</h3>
            <ul>
                <li><kbd>Ctrl+N</kbd> - New File</li>
                <li><kbd>Ctrl+S</kbd> - Save File</li>
                <li><kbd>Ctrl+Shift+S</kbd> - Save All</li>
                <li>Right-click files for context menu</li>
            </ul>
        </div>
        
        <div class="feature">
            <h3>🔧 Getting Started</h3>
            <ul>
                <li>Select a project folder</li>
                <li>Create new files/folders</li>
                <li>Import existing files</li>
                <li>Everything auto-saves!</li>
            </ul>
        </div>
    </div>
</body>
</html>`;

                    this.setContent(defaultContent, false);
                }

                setContent(content, isDirty = true) {
                    state.editorContent = content;
                    dom.htmlInput.value = content;
                    this._updateSyntaxHighlight();
                    preview.scheduleUpdate(content);
                    markDirty(isDirty);
                }

                setSelection(from, to) {
                    const el = dom.htmlInput;
                    el.focus();
                    el.setSelectionRange(from, to);
                    // sync scroll into view roughly
                    const before = state.editorContent.slice(0, from);
                    const lines = (before.match(/\n/g) || []).length;
                    const lineHeight = 21; // approx
                    el.scrollTop = Math.max(0, lines * lineHeight - 80);
                }

                _updateSyntaxHighlight() {
                    dom.codeElement.textContent = state.editorContent;
                    Prism.highlightElement(dom.codeElement);
                }

                extractTitleFromContent() {
                    const match = state.editorContent.match(/<title>([\s\S]*?)<\/title>/i);
                    return match ? match[1].trim() || 'untitled' : 'untitled';
                }
            }

            // ===== PREVIEW ENGINE =====
            class PreviewEngine {
                constructor() { 
                    this._schedule = null; 
                    this.setupEventListeners();
                }

                setupEventListeners() {
                    document.getElementById('refresh-preview-btn').addEventListener('click', () => {
                        this.updatePreview(state.editorContent);
                        showToast('Preview refreshed', 'info');
                    });

                    document.getElementById('toggle-console-btn').addEventListener('click', () => {
                        dom.consoleOutputContainer.classList.toggle('hidden');
                    });
                }

                scheduleUpdate(content) {
                    if (this._schedule) cancelAnimationFrame(this._schedule);
                    this._schedule = requestAnimationFrame(() => this.updatePreview(content));
                }

                updatePreview(content) {
                    try {
                        dom.previewIframe.srcdoc = content || '';
                        setTimeout(() => this.captureConsoleLogs(), 50);
                    } catch (err) {
                        reportError('Preview update failed', err);
                    }
                }

                captureConsoleLogs() {
                    const win = dom.previewIframe.contentWindow;
                    if (!win) return;

                    const logToUi = (type, args) => {
                        const message = args.map(arg => 
                            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg
                        ).join(' ');
                        
                        const entry = document.createElement('div');
                        const color = type === 'error' ? 'text-red-400' : 
                                     type === 'warn' ? 'text-yellow-400' : 'text-slate-300';
                        entry.className = `${color} py-1`;
                        entry.textContent = `[${type.toUpperCase()}] ${message}`;
                        
                        dom.consoleOutput.appendChild(entry);
                        dom.consoleOutput.scrollTop = dom.consoleOutput.scrollHeight;
                        
                        // Show console if there's an error
                        if (type === 'error') {
                            dom.consoleOutputContainer.classList.remove('hidden');
                        }
                    };

                    win.onerror = (msg, src, line, col, err) => {
                        logToUi('error', [`${err?.name || 'Error'}: ${msg} (line ${line})`]);
                    };

                    const oldConsole = win.console;
                    win.console = {
                        ...oldConsole,
                        log: (...args) => { logToUi('log', args); oldConsole.log(...args); },
                        error: (...args) => { logToUi('error', args); oldConsole.error(...args); },
                        warn: (...args) => { logToUi('warn', args); oldConsole.warn(...args); },
                        info: (...args) => { logToUi('info', args); oldConsole.info(...args); },
                    };
                }
            }

            // ===== PANEL MANAGER =====
            class PanelManager {
                constructor() {
                    this.setupToggleButtons();
                    this.restorePanelStates();
                    this.setupResizers();
                }

                setupToggleButtons() {
                    document.getElementById('files-toggle').addEventListener('click', () => {
                        this.togglePane('files', dom.filesPane);
                    });

                    document.getElementById('code-toggle').addEventListener('click', () => {
                        this.togglePane('code', dom.codePane);
                    });

                    document.getElementById('preview-toggle').addEventListener('click', () => {
                        this.togglePane('preview', dom.previewPane);
                    });
                }

                togglePane(paneName, paneElement) {
                    const isCollapsed = paneElement.classList.toggle('collapsed');
                    state.settings.panelStates[paneName] = isCollapsed;
                    
                    // Update toggle button icon
                    const toggle = paneElement.querySelector('.pane-toggle');
                    const svg = toggle.querySelector('svg');
                    
                    if (paneName === 'files' || paneName === 'code') {
                        // Left-side panels
                        svg.style.transform = isCollapsed ? 'rotate(180deg)' : 'rotate(0deg)';
                    } else {
                        // Right-side panels  
                        svg.style.transform = isCollapsed ? 'rotate(180deg)' : 'rotate(0deg)';
                    }
                    
                    saveSettings();
                }

                restorePanelStates() {
                    Object.entries(state.settings.panelStates).forEach(([paneName, isCollapsed]) => {
                        if (isCollapsed) {
                            const paneElement = document.getElementById(`${paneName}-pane`);
                            if (paneElement) {
                                paneElement.classList.add('collapsed');
                                
                                const toggle = paneElement.querySelector('.pane-toggle svg');
                                if (toggle) {
                                    toggle.style.transform = 'rotate(180deg)';
                                }
                            }
                        }
                    });

                    // Restore persisted sizes
                    const sizes = state.settings.panelSizes || {};
                    const filesW = parseInt(sizes.filesWidth, 10);
                    if (!isNaN(filesW) && filesW > 0) {
                        dom.filesPane.style.width = `${filesW}px`;
                        dom.filesPane.style.flexBasis = `${filesW}px`;
                    }
                    const codeW = parseInt(sizes.codeWidth, 10);
                    if (!isNaN(codeW) && codeW > 0) {
                        dom.codePane.style.flex = '0 0 auto';
                        dom.previewPane.style.flex = '1 1 0';
                        dom.codePane.style.width = `${codeW}px`;
                        dom.codePane.style.flexBasis = `${codeW}px`;
                    }
                }

                setupResizers() {
                    const filesPane = dom.filesPane;
                    const codePane = dom.codePane;
                    const previewPane = dom.previewPane;

                    const resizerFC = document.getElementById('resizer-files-code');
                    const resizerCP = document.getElementById('resizer-code-preview');

                    const clamp = (val, min, max) => Math.min(Math.max(val, min), max);

                    const makeDrag = (resizerEl, onMove) => {
                        if (!resizerEl) return;
                        let dragging = false;

                        const onMouseMove = (e) => {
                            if (!dragging) return;
                            onMove(e);
                        };
                        const onMouseUp = () => {
                            if (!dragging) return;
                            dragging = false;
                            document.body.style.cursor = 'default';
                            resizerEl.classList.remove('is-dragging');

                            // Persist sizes on drag end
                            const filesWidth = parseInt(dom.filesPane.style.flexBasis || dom.filesPane.style.width || 300, 10) || 300;
                            const codeWidth = parseInt(dom.codePane.style.flexBasis || dom.codePane.style.width || 0, 10) || null;
                            state.settings.panelSizes = { filesWidth, codeWidth };
                            saveSettings();
                            PersistenceManager.save();
                        };

                        resizerEl.addEventListener('mousedown', (e) => {
                            e.preventDefault();
                            dragging = true;
                            document.body.style.cursor = 'col-resize';
                            resizerEl.classList.add('is-dragging');
                            window.addEventListener('mousemove', onMouseMove);
                            window.addEventListener('mouseup', onMouseUp, { once: true });
                        });

                        // Basic keyboard resizing for accessibility
                        resizerEl.addEventListener('keydown', (e) => {
                            const step = (e.shiftKey ? 40 : 10);
                            if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                                e.preventDefault();
                                const delta = e.key === 'ArrowLeft' ? -step : step;
                                onMove({ clientX: (resizerEl.getBoundingClientRect().left + delta) });
                            }
                        });
                    };

                    // Files <-> Code
                    makeDrag(resizerFC, (e) => {
                        const containerLeft = dom.app.getBoundingClientRect().left;
                        const x = e.clientX - containerLeft;
                        const minFiles = 220;
                        const maxFiles = Math.max(minFiles, window.innerWidth * 0.5);
                        const newFilesW = clamp(x, minFiles, maxFiles);

                        filesPane.style.width = `${newFilesW}px`;
                        filesPane.style.flexBasis = `${newFilesW}px`;
                    });

                    // Code <-> Preview
                    makeDrag(resizerCP, (e) => {
                        const containerLeft = dom.app.getBoundingClientRect().left;
                        const x = e.clientX - containerLeft;

                        const filesW = filesPane.classList.contains('collapsed') ? 32 : (parseInt(filesPane.style.flexBasis || filesPane.style.width || 300, 10) || 300);
                        const total = window.innerWidth - (filesPane.classList.contains('collapsed') ? 32 : filesW);

                        // Compute code width relative to container
                        const codeLeft = filesPane.classList.contains('collapsed') ? 32 : filesW;
                        const newCodeW = clamp(x - codeLeft, 250, total - 250);

                        codePane.style.flex = '0 0 auto';
                        previewPane.style.flex = '1 1 0';

                        codePane.style.width = `${newCodeW}px`;
                        codePane.style.flexBasis = `${newCodeW}px`;
                    });
                }
            }

            // ===== INITIALIZATION =====
            const fileManager = new FileManager();
            const editor = new EditorController();
            const preview = new PreviewEngine();
            const panelManager = new PanelManager();
            const searchManager = new SearchManager();

            // ===== EVENT LISTENERS =====
            document.getElementById('save-btn').addEventListener('click', () => {
                fileManager.saveCurrentFile();
            });

            dom.saveAllBtn.addEventListener('click', () => {
                fileManager.saveAllFiles();
            });

            document.getElementById('dark-mode-toggle').addEventListener('click', () => {
                state.settings.darkMode = !state.settings.darkMode;
                document.documentElement.classList.toggle('dark', state.settings.darkMode);
                saveSettings();
            });

            // Keyboard shortcuts
            window.addEventListener('keydown', (e) => {
                const mod = e.metaKey || e.ctrlKey;
                
                if (mod && e.key.toLowerCase() === 's') {
                    e.preventDefault();
                    if (e.shiftKey) {
                        fileManager.saveAllFiles();
                    } else {
                        fileManager.saveCurrentFile();
                    }
                } else if (mod && e.key.toLowerCase() === 'n') {
                    e.preventDefault();
                    fileManager.showNewFileModal();
                } else if (mod && e.key.toLowerCase() === 'f') {
                    e.preventDefault();
                    document.getElementById('open-search-btn').click();
                } else if (mod && e.shiftKey && e.key.toLowerCase() === 'h') {
                    // Replace all shortcut: Ctrl+Shift+H
                    e.preventDefault();
                    document.getElementById('search-replace-all')?.click();
                }
            });

            // Drag and drop
            dom.editorContainer.addEventListener('dragover', (e) => {
                e.preventDefault();
                dom.editorContainer.classList.add('drag-over');
            });

            dom.editorContainer.addEventListener('dragleave', () => {
                dom.editorContainer.classList.remove('drag-over');
            });

            dom.editorContainer.addEventListener('drop', async (e) => {
                e.preventDefault();
                dom.editorContainer.classList.remove('drag-over');
                
                const file = e.dataTransfer.files?.[0];
                if (!file || !file.name.toLowerCase().match(/\.(html|htm)$/)) {
                    showToast('Only HTML files are supported.', 'error');
                    return;
                }

                const content = await file.text();
                const fileName = generateUniqueFileName(file.name.replace(/\.(html|htm)$/, ''), '.html');
                
                state.virtualFiles.set(fileName, {
                    content: content,
                    isVirtual: true,
                    created: Date.now()
                });
                
                state.currentFile = null;
                state.currentFilePath = fileName;
                editor.setContent(content, true);
                fileManager.renderFileTree();
                PersistenceManager.save();
                
                showToast(`Imported ${fileName}`, 'success');
            });

</body>
</html>
