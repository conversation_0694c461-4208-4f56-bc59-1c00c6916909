"""
Test Script for HTML Previewer Pro - Collapse Functionality
This script opens the HTML file and provides instructions for testing the improved collapse feature.
"""

import webbrowser
import os
from pathlib import Path

def test_collapse_functionality():
    """Test the collapse functionality of the HTML Previewer"""
    
    # Get the HTML file path
    project_root = Path(__file__).parent.parent
    html_file = project_root / "HTML_Previewer.html"
    
    if not html_file.exists():
        print("❌ HTML_Previewer.html not found!")
        return False
    
    print("🚀 HTML Previewer Pro - Collapse Functionality Test")
    print("=" * 60)
    print()
    print("Opening HTML Previewer in your browser...")
    
    # Open in browser
    try:
        webbrowser.open(f'file://{html_file.absolute()}')
        print("✅ HTML Previewer opened successfully!")
        print()
        
        # Provide testing instructions
        print("🧪 TESTING INSTRUCTIONS:")
        print("=" * 40)
        print()
        print("1. COLLAPSE EDITOR PANEL:")
        print("   - Click the toggle button on the right side of the editor panel")
        print("   - The editor should collapse to ~32px width")
        print("   - The PREVIEW panel should EXPAND to fill the available space")
        print("   - Toggle button should remain visible and accessible")
        print()
        
        print("2. COLLAPSE PREVIEW PANEL:")
        print("   - Click the toggle button on the right side of the preview panel")
        print("   - The preview should collapse to ~32px width")
        print("   - The EDITOR panel should EXPAND to fill the available space")
        print("   - Toggle button should remain visible and accessible")
        print()
        
        print("3. COLLAPSE FILES PANEL:")
        print("   - Click the toggle button on the left side of the files panel")
        print("   - The files panel should collapse to ~32px width")
        print("   - Both EDITOR and PREVIEW should get more space")
        print("   - Toggle button should remain visible and accessible")
        print()
        
        print("4. TEST COMBINATIONS:")
        print("   - Try collapsing multiple panels at once")
        print("   - Remaining visible panels should expand to use ALL available space")
        print("   - Toggle buttons should always be accessible")
        print("   - Panels should remember their state when you refresh the page")
        print()
        
        print("✅ EXPECTED BEHAVIOR:")
        print("   - Collapsed panels should only take ~32px width")
        print("   - Remaining panels should expand to fill available space")
        print("   - No wasted empty space in the layout")
        print("   - Smooth animations during collapse/expand")
        print("   - Toggle buttons always visible and clickable")
        print()
        
        print("❌ PROBLEMS TO REPORT:")
        print("   - Panels don't expand when others are collapsed")
        print("   - Toggle buttons become inaccessible")
        print("   - Layout breaks or looks awkward")
        print("   - Empty space left unused")
        print("   - Animation issues or jerky transitions")
        print()
        
        print("🔄 REFRESH TEST:")
        print("   - Collapse some panels, then refresh the page")
        print("   - Panel states should be remembered (persistent storage)")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ Error opening browser: {e}")
        return False

def main():
    """Main function"""
    print("Starting collapse functionality test...")
    success = test_collapse_functionality()
    
    if success:
        print("✅ Test setup completed!")
        print("📋 Please follow the testing instructions above.")
        print("🔍 Check if the collapse functionality now provides more space to remaining panels.")
    else:
        print("❌ Test setup failed!")
    
    input("\nPress Enter to exit...")
    return success

if __name__ == "__main__":
    main() 