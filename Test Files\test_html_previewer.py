"""
HTML Previewer Pro - Test Script
Tests the enhanced HTML Previewer functionality and verifies all features are present.
"""

import os
import sys
import re
import webbrowser
from pathlib import Path

class HTMLPreviewerTester:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.html_file = self.project_root / "HTML_Previewer.html"
        self.test_results = []
        
    def log_test(self, test_name, passed, details=""):
        """Log test results"""
        status = "✅ PASS" if passed else "❌ FAIL"
        self.test_results.append({
            'name': test_name,
            'passed': passed,
            'details': details,
            'status': status
        })
        print(f"{status}: {test_name}")
        if details:
            print(f"    {details}")
        print()

    def test_file_exists(self):
        """Test if the HTML file exists"""
        exists = self.html_file.exists()
        self.log_test(
            "HTML File Exists",
            exists,
            f"File path: {self.html_file}" if exists else "HTML_Previewer.html not found"
        )
        return exists

    def test_file_content(self):
        """Test if the HTML file contains required content"""
        if not self.html_file.exists():
            return False
            
        try:
            content = self.html_file.read_text(encoding='utf-8')
            
            # Test for key features
            tests = [
                ("Title Updated", "HTML Previewer Pro" in content),
                ("New File Button", 'id="new-file-btn"' in content),
                ("New Folder Button", 'id="new-folder-btn"' in content),
                ("Import File Button", 'id="import-file-btn"' in content),
                ("Save All Button", 'id="save-all-btn"' in content),
                ("Pane Toggle System", 'class="pane-toggle' in content),
                ("Context Menu", 'id="context-menu"' in content),
                ("New File Modal", 'id="new-file-modal"' in content),
                ("New Folder Modal", 'id="new-folder-modal"' in content),
                ("PersistenceManager Class", 'class PersistenceManager' in content),
                ("FileManager Class", 'class FileManager' in content),
                ("EditorController Class", 'class EditorController' in content),
                ("PreviewEngine Class", 'class PreviewEngine' in content),
                ("PanelManager Class", 'class PanelManager' in content),
                ("LocalStorage Integration", 'localStorage.getItem' in content),
                ("Keyboard Shortcuts", 'Ctrl+N' in content and 'Ctrl+S' in content),
                ("Virtual File System", 'virtualFiles' in content),
                ("Toast Notifications", 'showToast' in content),
                ("Dark Mode Toggle", 'dark-mode-toggle' in content),
                ("Status Indicators", 'status-indicator' in content)
            ]
            
            for test_name, condition in tests:
                self.log_test(test_name, condition)
                
            # Overall content test
            passed_tests = sum(1 for _, condition in tests if condition)
            total_tests = len(tests)
            
            self.log_test(
                "Content Features Test",
                passed_tests >= total_tests * 0.9,  # 90% pass rate
                f"Passed: {passed_tests}/{total_tests} feature tests"
            )
            
            return passed_tests >= total_tests * 0.9
            
        except Exception as e:
            self.log_test("File Content Reading", False, f"Error: {str(e)}")
            return False

    def test_html_structure(self):
        """Test HTML structure and syntax"""
        if not self.html_file.exists():
            return False
            
        try:
            content = self.html_file.read_text(encoding='utf-8')
            
            # Basic HTML structure tests
            structure_tests = [
                ("DOCTYPE Declaration", content.strip().startswith('<!DOCTYPE html>')),
                ("HTML Tag", '<html' in content and '</html>' in content),
                ("Head Section", '<head>' in content and '</head>' in content),
                ("Body Section", '<body>' in content and '</body>' in content),
                ("Required Meta Tags", 'charset="UTF-8"' in content),
                ("Title Tag", '<title>' in content and '</title>' in content),
                ("CSS Styles", '<style>' in content and '</style>' in content),
                ("JavaScript", '<script>' in content and '</script>' in content)
            ]
            
            for test_name, condition in structure_tests:
                self.log_test(test_name, condition)
                
            passed_tests = sum(1 for _, condition in structure_tests if condition)
            return passed_tests == len(structure_tests)
            
        except Exception as e:
            self.log_test("HTML Structure Test", False, f"Error: {str(e)}")
            return False

    def test_file_size(self):
        """Test file size to ensure content was added"""
        if not self.html_file.exists():
            return False
            
        try:
            file_size = self.html_file.stat().st_size
            # Expecting a substantial file (at least 30KB for all the new features)
            min_size = 30 * 1024  # 30KB
            
            self.log_test(
                "File Size Check",
                file_size >= min_size,
                f"File size: {file_size:,} bytes (minimum expected: {min_size:,} bytes)"
            )
            
            return file_size >= min_size
            
        except Exception as e:
            self.log_test("File Size Check", False, f"Error: {str(e)}")
            return False

    def test_css_features(self):
        """Test CSS features and styling"""
        if not self.html_file.exists():
            return False
            
        try:
            content = self.html_file.read_text(encoding='utf-8')
            
            css_tests = [
                ("CSS Custom Properties", ':root {' in content and '--bg-primary' in content),
                ("Dark Mode Styles", 'html.dark' in content),
                ("Pane Toggle Styles", '.pane-toggle' in content),
                ("File Item Styles", '.file-item' in content),
                ("Context Menu Styles", '.context-menu' in content),
                ("Modal Styles", '.modal' in content),
                ("Status Indicator Styles", '.status-indicator' in content),
                ("Animation Keyframes", '@keyframes' in content),
                ("Responsive Design", 'flex' in content and 'grid' in content)
            ]
            
            for test_name, condition in css_tests:
                self.log_test(test_name, condition)
                
            passed_tests = sum(1 for _, condition in css_tests if condition)
            return passed_tests >= len(css_tests) * 0.8  # 80% pass rate
            
        except Exception as e:
            self.log_test("CSS Features Test", False, f"Error: {str(e)}")
            return False

    def test_javascript_features(self):
        """Test JavaScript functionality"""
        if not self.html_file.exists():
            return False
            
        try:
            content = self.html_file.read_text(encoding='utf-8')
            
            js_tests = [
                ("Event Listeners", 'addEventListener' in content),
                ("LocalStorage Usage", 'localStorage.setItem' in content and 'localStorage.getItem' in content),
                ("DOM Manipulation", 'document.getElementById' in content),
                ("File API", 'FileReader' in content or 'webkitRelativePath' in content),
                ("Class Definitions", 'class FileManager' in content),
                ("Arrow Functions", '=>' in content),
                ("Async/Await", 'async' in content and 'await' in content),
                ("Error Handling", 'try {' in content and 'catch' in content),
                ("Keyboard Events", 'keydown' in content),
                ("Drag and Drop", 'dragover' in content and 'drop' in content)
            ]
            
            for test_name, condition in js_tests:
                self.log_test(test_name, condition)
                
            passed_tests = sum(1 for _, condition in js_tests if condition)
            return passed_tests >= len(js_tests) * 0.8  # 80% pass rate
            
        except Exception as e:
            self.log_test("JavaScript Features Test", False, f"Error: {str(e)}")
            return False

    def open_in_browser(self):
        """Open the HTML file in the default browser for manual testing"""
        if self.html_file.exists():
            try:
                webbrowser.open(f'file://{self.html_file.absolute()}')
                self.log_test(
                    "Browser Launch",
                    True,
                    "HTML file opened in default browser for manual testing"
                )
                return True
            except Exception as e:
                self.log_test("Browser Launch", False, f"Error: {str(e)}")
                return False
        return False

    def run_all_tests(self):
        """Run all tests and generate report"""
        print("🚀 HTML Previewer Pro - Test Suite")
        print("=" * 50)
        print()
        
        tests = [
            self.test_file_exists,
            self.test_file_size,
            self.test_html_structure,
            self.test_file_content,
            self.test_css_features,
            self.test_javascript_features
        ]
        
        all_passed = True
        for test in tests:
            try:
                result = test()
                if not result:
                    all_passed = False
            except Exception as e:
                print(f"❌ FAIL: {test.__name__} - Error: {str(e)}")
                all_passed = False
        
        # Generate summary
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        
        passed_count = sum(1 for result in self.test_results if result['passed'])
        total_count = len(self.test_results)
        
        print(f"Total Tests: {total_count}")
        print(f"Passed: {passed_count}")
        print(f"Failed: {total_count - passed_count}")
        print(f"Success Rate: {(passed_count/total_count)*100:.1f}%")
        
        if all_passed:
            print("\n🎉 ALL MAJOR TESTS PASSED!")
            print("✨ HTML Previewer Pro is ready for use!")
        else:
            print("\n⚠️  Some tests failed. Please check the implementation.")
        
        # Ask if user wants to open in browser
        print("\n" + "=" * 50)
        user_input = input("Would you like to open the HTML Previewer in your browser for manual testing? (y/n): ")
        if user_input.lower() in ['y', 'yes']:
            self.open_in_browser()
        
        return all_passed

def main():
    """Main test function"""
    tester = HTMLPreviewerTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ Testing completed successfully!")
        print("🚀 HTML Previewer Pro is ready to use!")
    else:
        print("\n❌ Some tests failed. Please review the implementation.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 