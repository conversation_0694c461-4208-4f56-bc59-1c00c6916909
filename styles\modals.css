/* Context menu */
.context-menu {
  position: fixed;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 4px;
  box-shadow: 0 10px 25px rgba(0,0,0,0.15);
  z-index: 1000;
  min-width: 180px;
}
.context-menu-item {
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}
.context-menu-item:hover { background: var(--bg-primary); }
.context-menu-separator {
  height: 1px;
  background: var(--border-color);
  margin: 4px 0;
}

/* Modals (New File, New Folder, Delete Confirm, Search) */
.modal {
  position: fixed;
  inset: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modal-content {
  background: var(--bg-secondary);
  border-radius: 12px;
  padding: 24px;
  min-width: 400px;
  max-width: 90vw;
}
