// If using CDN: <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
import JSZip from 'jszip'; // if using npm

async function saveAllFiles() {
    if (state.modifiedFiles.size === 0) {
        showToast('No modified files to save.', 'info');
        return;
    }

    const zip = new JSZip();

    // Add all modified files to the zip
    for (const filePath of state.modifiedFiles) {
        let content = '';
        if (state.virtualFiles.has(filePath)) {
            content = state.virtualFiles.get(filePath).content;
        } else if (state.loadedFiles.has(filePath)) {
            content = state.loadedFiles.get(filePath).content;
        }
        // Use the full path for correct folder structure in the zip
        zip.file(filePath, content);
    }

    try {
        const blob = await zip.generateAsync({ type: 'blob' });
        const a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = `project-${Date.now()}.zip`;
        a.click();
        URL.revokeObjectURL(a.href);

        // Success: Clear modified flags
        state.modifiedFiles.clear();
        markDirty(false); // Update global dirty state
        this.renderFileTree(); // Re-render to remove modified indicators
        showToast('Project saved as a zip file!', 'success');
    } catch (err) {
        showToast(`Error creating zip file: ${err.message}`, 'error');
    }
}

async function renameFile(oldPath, newPath) {
    // Validate new name
    if (!newPath.trim()) {
        showToast('File name cannot be empty.', 'error');
        return false;
    }

    // Check if file exists
    if (!state.virtualFiles.has(oldPath) && !state.loadedFiles.has(oldPath)) {
        showToast('File does not exist.', 'error');
        return false;
    }

    // Check if new path already exists
    if (state.virtualFiles.has(newPath) || state.loadedFiles.has(newPath)) {
        showToast('A file with this name already exists.', 'error');
        return false;
    }

    // Update virtual files
    if (state.virtualFiles.has(oldPath)) {
        const fileData = state.virtualFiles.get(oldPath);
        state.virtualFiles.delete(oldPath);
        state.virtualFiles.set(newPath, fileData);
    }

    // Update loaded files
    if (state.loadedFiles.has(oldPath)) {
        const fileData = state.loadedFiles.get(oldPath);
        state.loadedFiles.delete(oldPath);
        state.loadedFiles.set(newPath, fileData);
    }

    // Update modified files set
    if (state.modifiedFiles.has(oldPath)) {
        state.modifiedFiles.delete(oldPath);
        state.modifiedFiles.add(newPath);
    }

    // Update current file if needed
    if (state.currentFile === oldPath) {
        state.currentFile = newPath;
    }

    // Update folder structure
    const oldDir = oldPath.substring(0, oldPath.lastIndexOf('/'));
    const newDir = newPath.substring(0, newPath.lastIndexOf('/'));

    if (oldDir !== newDir) {
        // Remove from old directory
        const oldDirFiles = state.folders.get(oldDir) || [];
        const updatedOldDirFiles = oldDirFiles.filter(f => f !== oldPath.split('/').pop());
        state.folders.set(oldDir, updatedOldDirFiles);

        // Add to new directory
        const newDirFiles = state.folders.get(newDir) || [];
        newDirFiles.push(newPath.split('/').pop());
        state.folders.set(newDir, newDirFiles);
    }

    // Re-render UI
    this.renderFileTree();
    this.renderEditor();

    // Persist changes
    saveState();

    showToast(`File renamed to ${newPath}`, 'success');
    return true;
}

async function deleteFileOrFolder(path, isFolder = false) {
    // Show confirmation modal
    const modal = document.getElementById('delete-confirmation-modal');
    const modalTitle = modal.querySelector('.modal-title');
    const modalMessage = modal.querySelector('.modal-message');
    const confirmButton = modal.querySelector('.confirm-button');
    const cancelButton = modal.querySelector('.cancel-button');

    // Set modal content based on type
    if (isFolder) {
        modalTitle.textContent = 'Delete Folder';
        modalMessage.textContent = `Are you sure you want to delete the folder "${path}" and all its contents? This action cannot be undone.`;
    } else {
        modalTitle.textContent = 'Delete File';
        modalMessage.textContent = `Are you sure you want to delete "${path}"? This action cannot be undone.`;
    }

    // Reset button states
    confirmButton.disabled = false;
    confirmButton.textContent = 'Delete';

    // Show modal
    modal.classList.remove('hidden');

    // Focus the cancel button by default
    cancelButton.focus();

    // Handle keyboard events
    const handleKeyDown = (e) => {
        if (e.key === 'Escape') {
            closeModal();
        }
    };

    // Handle confirmation
    const handleConfirm = () => {
        confirmButton.disabled = true;
        confirmButton.textContent = 'Deleting...';

        // Perform deletion
        if (isFolder) {
            deleteFolder(path);
        } else {
            deleteSingleFile(path);
        }

        // Close modal
        closeModal();
    };

    // Handle cancellation
    const handleCancel = () => {
        closeModal();
    };

    // Close modal function
    const closeModal = () => {
        modal.classList.add('hidden');
        document.removeEventListener('keydown', handleKeyDown);
        confirmButton.removeEventListener('click', handleConfirm);
        cancelButton.removeEventListener('click', handleCancel);
    };

    // Add event listeners
    document.addEventListener('keydown', handleKeyDown);
    confirmButton.addEventListener('click', handleConfirm);
    cancelButton.addEventListener('click', handleCancel);
}

async function deleteSingleFile(path) {
    // Remove from virtual files if exists
    if (state.virtualFiles.has(path)) {
        state.virtualFiles.delete(path);
    }

    // Remove from loaded files if exists
    if (state.loadedFiles.has(path)) {
        state.loadedFiles.delete(path);
    }

    // Remove from modified files if exists
    if (state.modifiedFiles.has(path)) {
        state.modifiedFiles.delete(path);
    }

    // Remove from current file if needed
    if (state.currentFile === path) {
        state.currentFile = null;
    }

    // Remove from folder structure
    const dir = path.substring(0, path.lastIndexOf('/'));
    const dirFiles = state.folders.get(dir) || [];
    const updatedDirFiles = dirFiles.filter(f => f !== path.split('/').pop());
    state.folders.set(dir, updatedDirFiles);

    // Re-render UI
    this.renderFileTree();
    this.renderEditor();

    // Persist changes
    saveState();

    showToast(`File "${path}" deleted successfully.`, 'success');
}

// Error codes for file operations
const ErrorCodes = {
    FILE_NOT_FOUND: 'FILE_NOT_FOUND',
    FOLDER_NOT_FOUND: 'FOLDER_NOT_FOUND',
    INVALID_PATH: 'INVALID_PATH',
    PERMISSION_DENIED: 'PERMISSION_DENIED',
    UNKNOWN_ERROR: 'UNKNOWN_ERROR'
};

// Centralized error handling utility
function safeAction(action, errorCode) {
    try {
        return action();
    } catch (error) {
        reportError(error, errorCode);
        return false;
    }
}

// Error reporting with user feedback
function reportError(error, errorCode) {
    console.error(`Error [${errorCode}]:`, error);

    let userMessage = 'An unexpected error occurred. Please try again.';
    switch (errorCode) {
        case ErrorCodes.FILE_NOT_FOUND:
            userMessage = 'The file could not be found.';
            break;
        case ErrorCodes.FOLDER_NOT_FOUND:
            userMessage = 'The folder could not be found.';
            break;
        case ErrorCodes.INVALID_PATH:
            userMessage = 'The path is invalid or contains invalid characters.';
            break;
        case ErrorCodes.PERMISSION_DENIED:
            userMessage = 'You do not have permission to perform this operation.';
            break;
    }

    showToast(userMessage, 'error');
    return false;
}

// Enhanced file operations with error handling
async function deleteFolder(path) {
    return safeAction(() => {
        // Validate path
        if (!path || typeof path !== 'string') {
            throw new Error('Invalid path provided');
        }

        // Check if folder exists
        if (!state.folders.has(path)) {
            throw new Error(`Folder not found: ${path}`);
        }

        // Get all files in the folder and its subfolders
        const filesToDelete = getAllFilesInFolder(path);

        // Delete all files
        filesToDelete.forEach(filePath => {
            safeAction(() => {
                // Remove from virtual files if exists
                if (state.virtualFiles.has(filePath)) {
                    state.virtualFiles.delete(filePath);
                }

                // Remove from loaded files if exists
                if (state.loadedFiles.has(filePath)) {
                    state.loadedFiles.delete(filePath);
                }

                // Remove from modified files if exists
                if (state.modifiedFiles.has(filePath)) {
                    state.modifiedFiles.delete(filePath);
                }

                // Remove from current file if needed
                if (state.currentFile === filePath) {
                    state.currentFile = null;
                }
            }, ErrorCodes.UNKNOWN_ERROR);
        });

        // Remove folder from structure
        state.folders.delete(path);

        // Remove folder from parent directory
        const parentDir = path.substring(0, path.lastIndexOf('/'));
        if (parentDir) {
            const parentDirFiles = state.folders.get(parentDir) || [];
            const updatedParentDirFiles = parentDirFiles.filter(f => f !== path.split('/').pop());
            state.folders.set(parentDir, updatedParentDirFiles);
        }

        // Re-render UI
        this.renderFileTree();
        this.renderEditor();

        // Persist changes
        saveState();

        showToast(`Folder "${path}" and all its contents deleted successfully.`, 'success');
        return true;
    }, ErrorCodes.FOLDER_NOT_FOUND);
}

// Enhanced file operations with error handling
async function deleteSingleFile(path) {
    return safeAction(() => {
        // Validate path
        if (!path || typeof path !== 'string') {
            throw new Error('Invalid path provided');
        }

        // Check if file exists
        if (!state.virtualFiles.has(path) && !state.loadedFiles.has(path)) {
            throw new Error(`File not found: ${path}`);
        }

        // Remove from virtual files if exists
        if (state.virtualFiles.has(path)) {
            state.virtualFiles.delete(path);
        }

        // Remove from loaded files if exists
        if (state.loadedFiles.has(path)) {
            state.loadedFiles.delete(path);
        }

        // Remove from modified files if exists
        if (state.modifiedFiles.has(path)) {
            state.modifiedFiles.delete(path);
        }

        // Remove from current file if needed
        if (state.currentFile === path) {
            state.currentFile = null;
        }

        // Remove from folder structure
        const dir = path.substring(0, path.lastIndexOf('/'));
        const dirFiles = state.folders.get(dir) || [];
        const updatedDirFiles = dirFiles.filter(f => f !== path.split('/').pop());
        state.folders.set(dir, updatedDirFiles);

        // Re-render UI
        this.renderFileTree();
        this.renderEditor();

        // Persist changes
        saveState();

        showToast(`File "${path}" deleted successfully.`, 'success');
        return true;
    }, ErrorCodes.FILE_NOT_FOUND);
}

// Enhanced file operations with error handling
async function renameFile(oldPath, newPath) {
    return safeAction(() => {
        // Validate paths
        if (!oldPath || !newPath || typeof oldPath !== 'string' || typeof newPath !== 'string') {
            throw new Error('Invalid paths provided');
        }

        // Validate new name
        if (!newPath.trim()) {
            throw new Error('File name cannot be empty');
        }

        // Check if file exists
        if (!state.virtualFiles.has(oldPath) && !state.loadedFiles.has(oldPath)) {
            throw new Error(`File not found: ${oldPath}`);
        }

        // Check if new path already exists
        if (state.virtualFiles.has(newPath) || state.loadedFiles.has(newPath)) {
            throw new Error(`A file with this name already exists: ${newPath}`);
        }

        // Update virtual files
        if (state.virtualFiles.has(oldPath)) {
            const fileData = state.virtualFiles.get(oldPath);
            state.virtualFiles.delete(oldPath);
            state.virtualFiles.set(newPath, fileData);
        }

        // Update loaded files
        if (state.loadedFiles.has(oldPath)) {
            const fileData = state.loadedFiles.get(oldPath);
            state.loadedFiles.delete(oldPath);
            state.loadedFiles.set(newPath, fileData);
        }

        // Update modified files set
        if (state.modifiedFiles.has(oldPath)) {
            state.modifiedFiles.delete(oldPath);
            state.modifiedFiles.add(newPath);
        }

        // Update current file if needed
        if (state.currentFile === oldPath) {
            state.currentFile = newPath;
        }

        // Update folder structure
        const oldDir = oldPath.substring(0, oldPath.lastIndexOf('/'));
        const newDir = newPath.substring(0, newPath.lastIndexOf('/'));

        if (oldDir !== newDir) {
            // Remove from old directory
            const oldDirFiles = state.folders.get(oldDir) || [];
            const updatedOldDirFiles = oldDirFiles.filter(f => f !== oldPath.split('/').pop());
            state.folders.set(oldDir, updatedOldDirFiles);

            // Add to new directory
            const newDirFiles = state.folders.get(newDir) || [];
            newDirFiles.push(newPath.split('/').pop());
            state.folders.set(newDir, newDirFiles);
        }

        // Re-render UI
        this.renderFileTree();
        this.renderEditor();

        // Persist changes
        saveState();

        showToast(`File renamed to ${newPath}`, 'success');
        return true;
    }, ErrorCodes.FILE_NOT_FOUND);
}

// Enhanced file operations with error handling
async function saveAllFiles() {
    return safeAction(async () => {
        if (state.modifiedFiles.size === 0) {
            showToast('No modified files to save.', 'info');
            return false;
        }

        const zip = new JSZip();

        // Add all modified files to the zip
        for (const filePath of state.modifiedFiles) {
            let content = '';
            if (state.virtualFiles.has(filePath)) {
                content = state.virtualFiles.get(filePath).content;
            } else if (state.loadedFiles.has(filePath)) {
                content = state.loadedFiles.get(filePath).content;
            }
            // Use the full path for correct folder structure in the zip
            zip.file(filePath, content);
        }

        try {
            const blob = await zip.generateAsync({ type: 'blob' });
            const a = document.createElement('a');
            a.href = URL.createObjectURL(blob);
            a.download = `project-${Date.now()}.zip`;
            a.click();
            URL.revokeObjectURL(a.href);

            // Success: Clear modified flags
            state.modifiedFiles.clear();
            markDirty(false); // Update global dirty state
            this.renderFileTree(); // Re-render to remove modified indicators
            showToast('Project saved as a zip file!', 'success');
            return true;
        } catch (err) {
            throw new Error(`Error creating zip file: ${err.message}`);
        }
    }, ErrorCodes.UNKNOWN_ERROR);
}

// Search and replace functionality
async function searchAndReplace(options) {
    return safeAction(() => {
        const {
            searchTerm,
            replaceTerm,
            isRegex = false,
            isCaseSensitive = false,
            isWholeWord = false,
            currentFileOnly = false
        } = options;

        // Validate search term
        if (!searchTerm || typeof searchTerm !== 'string') {
            throw new Error('Search term is required and must be a string');
        }

        // Prepare regex flags
        const flags = [];
        if (!isCaseSensitive) flags.push('i');
        if (isWholeWord) flags.push('\\b');

        // Create regex pattern
        let regexPattern;
        try {
            regexPattern = isRegex
                ? new RegExp(searchTerm, flags.join(''))
                : new RegExp(escapeRegExp(searchTerm), flags.join(''));
        } catch (err) {
            throw new Error(`Invalid regex pattern: ${err.message}`);
        }

        // Get files to search
        const filesToSearch = currentFileOnly
            ? [state.currentFile]
            : Array.from(state.virtualFiles.keys()).concat(Array.from(state.loadedFiles.keys()));

        // Perform search and collect results
        const results = [];
        let totalMatches = 0;
        let totalFilesWithMatches = 0;

        filesToSearch.forEach(filePath => {
            if (!filePath) return;

            // Get file content
            let content = '';
            if (state.virtualFiles.has(filePath)) {
                content = state.virtualFiles.get(filePath).content;
            } else if (state.loadedFiles.has(filePath)) {
                content = state.loadedFiles.get(filePath).content;
            }

            // Skip empty files
            if (!content) return;

            // Find matches
            const matches = [];
            let lastIndex = 0;
            let match;

            while ((match = regexPattern.exec(content)) !== null) {
                // Get context around the match
                const start = Math.max(0, match.index - 20);
                const end = Math.min(content.length, match.index + match[0].length + 20);
                const context = content.substring(start, end);

                matches.push({
                    match: match[0],
                    index: match.index,
                    context: context.trim()
                });

                // Prevent infinite loops with zero-width matches
                if (match.index === regexPattern.lastIndex) {
                    regexPattern.lastIndex++;
                }
            }

            if (matches.length > 0) {
                results.push({
                    filePath,
                    matches
                });
                totalMatches += matches.length;
                totalFilesWithMatches++;
            }
        });

        // Show results
        if (results.length === 0) {
            showToast('No matches found.', 'info');
            return { results: [], totalMatches: 0, totalFilesWithMatches: 0 };
        }

        // Show summary
        showToast(`Found ${totalMatches} matches in ${totalFilesWithMatches} files.`, 'success');

        return {
            results,
            totalMatches,
            totalFilesWithMatches
        };
    }, ErrorCodes.UNKNOWN_ERROR);
}

// Helper function to escape regex special characters
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// Replace selected matches in a file
async function replaceSelectedMatches(filePath, matches, replaceTerm) {
    return safeAction(() => {
        // Validate parameters
        if (!filePath || !matches || !replaceTerm) {
            throw new Error('Invalid parameters for replaceSelectedMatches');
        }

        // Get file content
        let content = '';
        if (state.virtualFiles.has(filePath)) {
            content = state.virtualFiles.get(filePath).content;
        } else if (state.loadedFiles.has(filePath)) {
            content = state.loadedFiles.get(filePath).content;
        } else {
            throw new Error(`File not found: ${filePath}`);
        }

        // Sort matches by index in descending order to avoid index shifting
        const sortedMatches = [...matches].sort((a, b) => b.index - a.index);

        // Perform replacements
        let newContent = content;
        let replacementsMade = 0;

        sortedMatches.forEach(match => {
            // Get the exact match text from the original content
            const matchText = content.substr(match.index, match.match.length);

            // Replace only if the match hasn't been modified
            if (newContent.substr(match.index, match.match.length) === matchText) {
                newContent = newContent.substring(0, match.index) +
                             replaceTerm +
                             newContent.substring(match.index + match.match.length);
                replacementsMade++;
            }
        });

        // Update file content
        if (state.virtualFiles.has(filePath)) {
            state.virtualFiles.get(filePath).content = newContent;
        } else if (state.loadedFiles.has(filePath)) {
            state.loadedFiles.get(filePath).content = newContent;
        }

        // Mark file as modified
        state.modifiedFiles.add(filePath);

        // Re-render UI
        this.renderFileTree();
        this.renderEditor();

        // Persist changes
        saveState();

        showToast(`Replaced ${replacementsMade} occurrences in ${filePath}.`, 'success');
        return true;
    }, ErrorCodes.UNKNOWN_ERROR);
}

// Replace all matches across files
async function replaceAllMatches(searchResults, replaceTerm) {
    return safeAction(() => {
        if (!searchResults || !replaceTerm) {
            throw new Error('Invalid parameters for replaceAllMatches');
        }

        let totalReplacements = 0;

        // Process each file with matches
        searchResults.results.forEach(fileResult => {
            // Get file content
            let content = '';
            if (state.virtualFiles.has(fileResult.filePath)) {
                content = state.virtualFiles.get(fileResult.filePath).content;
            } else if (state.loadedFiles.has(fileResult.filePath)) {
                content = state.loadedFiles.get(fileResult.filePath).content;
            } else {
                return; // Skip if file not found
            }

            // Sort matches by index in descending order to avoid index shifting
            const sortedMatches = [...fileResult.matches].sort((a, b) => b.index - a.index);

            // Perform replacements
            let newContent = content;
            let replacementsMade = 0;

            sortedMatches.forEach(match => {
                // Get the exact match text from the original content
                const matchText = content.substr(match.index, match.match.length);

                // Replace only if the match hasn't been modified
                if (newContent.substr(match.index, match.match.length) === matchText) {
                    newContent = newContent.substring(0, match.index) +
                                 replaceTerm +
                                 newContent.substring(match.index + match.match.length);
                    replacementsMade++;
                }
            });

            // Update file content
            if (state.virtualFiles.has(fileResult.filePath)) {
                state.virtualFiles.get(fileResult.filePath).content = newContent;
            } else if (state.loadedFiles.has(fileResult.filePath)) {
                state.loadedFiles.get(fileResult.filePath).content = newContent;
            }

            // Mark file as modified
            state.modifiedFiles.add(fileResult.filePath);

            totalReplacements += replacementsMade;
        });

        // Re-render UI
        this.renderFileTree();
        this.renderEditor();

        // Persist changes
        saveState();

        showToast(`Replaced ${totalReplacements} occurrences across ${searchResults.results.length} files.`, 'success');
        return true;
    }, ErrorCodes.UNKNOWN_ERROR);
}

// Jump to a specific match in the editor
function jumpToMatch(filePath, matchIndex) {
    return safeAction(() => {
        // Validate parameters
        if (!filePath || matchIndex === undefined) {
            throw new Error('Invalid parameters for jumpToMatch');
        }

        // Check if file exists
        if (!state.virtualFiles.has(filePath) && !state.loadedFiles.has(filePath)) {
            throw new Error(`File not found: ${filePath}`);
        }

        // Set current file
        state.currentFile = filePath;

        // Get file content
        let content = '';
        if (state.virtualFiles.has(filePath)) {
            content = state.virtualFiles.get(filePath).content;
        } else if (state.loadedFiles.has(filePath)) {
            content = state.loadedFiles.get(filePath).content;
        }

        // Re-render UI to show the file
        this.renderFileTree();
        this.renderEditor();

        // Highlight the match in the editor
        // Note: This would need to be implemented in the editor component
        // For now, we'll just scroll to the match position
        if (typeof this.editor !== 'undefined' && this.editor.scrollToMatch) {
            this.editor.scrollToMatch(matchIndex);
        }

        return true;
    }, ErrorCodes.FILE_NOT_FOUND);
}

function getAllFilesInFolder(folderPath) {
    let files = [];

    // Add files directly in the folder
    const folderFiles = state.folders.get(folderPath) || [];
    files = files.concat(folderFiles.map(f => `${folderPath}/${f}`));

    // Recursively add files from subfolders
    const subfolders = Object.keys(state.folders).filter(f => f.startsWith(`${folderPath}/`));
    subfolders.forEach(subfolder => {
        files = files.concat(getAllFilesInFolder(subfolder));
    });

    return files;
}
