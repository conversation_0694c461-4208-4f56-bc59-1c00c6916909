"""
HTML Previewer Pro - Folder Tree Test
Tests the new expandable/collapsible folder tree functionality.
"""

import webbrowser
import os
from pathlib import Path

def test_folder_tree():
    """Test the folder tree functionality"""
    
    project_root = Path(__file__).parent.parent
    html_file = project_root / "HTML_Previewer.html"
    
    if not html_file.exists():
        print("❌ HTML_Previewer.html not found!")
        return False
    
    print("🌳 HTML Previewer Pro - Folder Tree Test")
    print("=" * 50)
    print()
    print("Opening HTML Previewer to test folder tree functionality...")
    
    try:
        webbrowser.open(f'file://{html_file.absolute()}')
        print("✅ HTML Previewer opened successfully!")
        print()
        
        print("🧪 FOLDER TREE TESTING INSTRUCTIONS:")
        print("=" * 45)
        print()
        
        print("1. 📁 LOAD A PROJECT WITH SUBFOLDERS:")
        print("   - Click 'Select Project Folder'")
        print("   - Choose a folder that contains subfolders with HTML files")
        print("   - The tree should now show folders with expand/collapse arrows")
        print()
        
        print("2. 🔽 TEST FOLDER EXPANSION:")
        print("   - Look for folders with small arrow icons (▶)")
        print("   - Click on a folder to expand/collapse it")
        print("   - Arrow should rotate to ▼ when expanded")
        print("   - Subfolders and files should appear nested underneath")
        print()
        
        print("3. 🔄 TEST NESTED STRUCTURE:")
        print("   - Folders should be properly indented based on depth")
        print("   - Files within folders should appear indented")
        print("   - Vertical lines should connect nested items")
        print("   - Each level should have appropriate spacing")
        print()
        
        print("4. 💾 TEST PERSISTENCE:")
        print("   - Expand some folders, collapse others")
        print("   - Refresh the page (F5)")
        print("   - Folder expansion states should be remembered")
        print("   - Tree structure should be restored exactly")
        print()
        
        print("5. 📄 TEST FILE ACCESS:")
        print("   - Click on files within nested folders")
        print("   - Files should load correctly in the editor")
        print("   - File path should be properly maintained")
        print("   - Active file should be highlighted in the tree")
        print()
        
        print("✅ EXPECTED BEHAVIOR:")
        print("   ▶ Folders with content show expand arrows")
        print("   ▼ Expanded folders show rotated arrows")
        print("   📁 Proper hierarchical indentation")
        print("   📄 Files nested under their parent folders")
        print("   🎯 Smooth expand/collapse animations")
        print("   💾 Expansion states persist across sessions")
        print("   🎨 Visual tree lines connecting items")
        print()
        
        print("❌ ISSUES TO REPORT:")
        print("   - Folders not expandable/collapsible")
        print("   - Missing or broken arrow icons")
        print("   - Incorrect nesting or indentation")
        print("   - Files not accessible within folders")
        print("   - Expansion states not saved")
        print("   - Visual glitches in tree structure")
        print()
        
        print("🎯 SAMPLE PROJECT STRUCTURE TO TEST:")
        print("   project/")
        print("   ├── index.html")
        print("   ├── css/")
        print("   │   └── styles.html")
        print("   ├── js/")
        print("   │   └── scripts.html")
        print("   └── pages/")
        print("       ├── about.html")
        print("       └── contact/")
        print("           └── form.html")
        print()
        
        print("🔍 WHAT TO VERIFY:")
        print("   1. All folders can be expanded/collapsed")
        print("   2. Nested structure displays correctly")
        print("   3. Files are accessible at any depth")
        print("   4. Visual hierarchy is clear and intuitive")
        print("   5. Performance is smooth with many folders")
        print("   6. State persistence works reliably")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ Error opening browser: {e}")
        return False

def create_sample_structure():
    """Create a sample folder structure for testing"""
    print("\n📁 CREATING SAMPLE TEST STRUCTURE:")
    print("=" * 35)
    
    try:
        # Create test folder structure
        test_root = Path(__file__).parent.parent / "Sample_Project"
        test_root.mkdir(exist_ok=True)
        
        # Create nested folders and files
        folders = {
            "css": ["styles.html", "reset.html"],
            "js": ["main.html", "utils.html"],
            "pages": ["about.html"],
            "pages/contact": ["form.html", "info.html"],
            "assets/images": ["gallery.html"],
            "components": ["header.html", "footer.html"]
        }
        
        # Create root files
        (test_root / "index.html").write_text("""<!DOCTYPE html>
<html><head><title>Sample Index</title></head>
<body><h1>Welcome to Sample Project</h1></body></html>""")
        
        (test_root / "README.html").write_text("""<!DOCTYPE html>
<html><head><title>Project README</title></head>
<body><h1>Project Documentation</h1></body></html>""")
        
        # Create folder structure
        for folder_path, files in folders.items():
            folder = test_root / folder_path
            folder.mkdir(parents=True, exist_ok=True)
            
            for file_name in files:
                content = f"""<!DOCTYPE html>
<html>
<head><title>{file_name.replace('.html', '').title()}</title></head>
<body>
    <h1>{file_name.replace('.html', '').title()} Page</h1>
    <p>This is a sample HTML file in {folder_path}</p>
</body>
</html>"""
                (folder / file_name).write_text(content)
        
        print(f"✅ Sample project created at: {test_root}")
        print(f"📂 Use this folder to test the tree functionality!")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating sample structure: {e}")
        return False

def main():
    """Main function"""
    print("Starting folder tree functionality test...")
    
    # Ask if user wants to create sample structure
    create_sample = input("Would you like to create a sample project structure for testing? (y/n): ")
    if create_sample.lower() in ['y', 'yes']:
        create_sample_structure()
    
    success = test_folder_tree()
    
    if success:
        print("✅ Test setup completed!")
        print("🌳 Follow the instructions above to test the folder tree functionality.")
        print("📋 Load the sample project or use your own folder with subfolders.")
    else:
        print("❌ Test setup failed!")
    
    input("\nPress Enter to exit...")
    return success

if __name__ == "__main__":
    main() 